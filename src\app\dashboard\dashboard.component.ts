import { HttpClient } from '@angular/common/http';
import { Component, HostListener, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbDatepickerConfig } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { PrimeNGConfig } from 'primeng-lts/api';
import { Subscription } from 'rxjs';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { AppConfig, AppConfigService } from '../app-config.service';
import { formatSecondsToHrMin, isAdmin } from '../config/commonHelper';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  basicData: any;
  basicOptions: any;
  subscription: Subscription;
  selectedButton: string = 'users';
  config: AppConfig | undefined;
  selectedPeriod: string = 'all';
  menuTitle: string = 'Dashboard';
  chartLabels: any;
  chartData: { data: any; label: string }[];

  userPieChartData: any;
  userPieChartOptions: any;
  subscriptionBarChartData: any;
  subscriptionBarChartOptions: any;

  selectedData: any;
  uploadedStories: number = 64;
  totalListingHour: string = 'N/A';
  publishedStories: number = 0;
  totalStoryTeller: number = 0;
  totalUser: number = 0;
  mostPlayedGenere: string = 'N/A';
  mostHearingLanguage: string = '';
  mostSubscribePlan: string = '';
  isAdmin = isAdmin();
  adminCards: any[] = [];
  storytellerCards: any[] = [];
  topStoryTellers: any;
  storyBasedOnHours: any;
  storyBasedOnRatings: any;
  initializeCards() {
    this.adminCards = [
      {
        count: this.totalStoryTeller,
        iconSrc: './assets/icons/storyTeller.svg',
        title: 'No of storytellers',
      },
      {
        count: this.uploadedStories,
        iconSrc: './assets/icons/Uploaded-Stories.svg',
        title: 'Number of stories uploaded',
      },
      {
        count: this.totalUser,
        iconSrc: './assets/icons/Users.svg',
        title: 'No of app users',
      },
      {
        count: this.mostPlayedGenere,
        iconSrc: './assets/icons/hours.svg',
        title: 'Most played genre',
      },
      {
        count: this.mostHearingLanguage,
        iconSrc: './assets/icons/hours.svg',
        title: 'Most heard language',
      },
      {
        count: this.mostSubscribePlan,
        iconSrc: './assets/icons/Subscriptions.svg',
        title: 'Most subscribed plan',
      },
    ];

    this.storytellerCards = [
      {
        count: this.uploadedStories,
        iconSrc: './assets/icons/Uploaded-Stories.svg',
        title: 'No of stories uploaded',
      },
      {
        count: this.publishedStories,
        iconSrc: './assets/icons/published-stories.svg',
        title: 'No of stories published',
      },
      {
        count: this.totalListingHour,
        iconSrc: './assets/icons/hours.svg',
        title: 'Total listening hours',
      },
    ];
  }
  // isMobile: boolean;

  constructor(
    private dataTransferService: DataTransferService,
    private primengConfig: PrimeNGConfig,
    private configService: AppConfigService,
    private ngxSpinnerService: NgxSpinnerService,
    private http: HttpClient,
    private router: Router,
    private toastr: ToastrService,
    private datepickerConfig: NgbDatepickerConfig
  ) { }

  ngOnInit() {
    this.getDashboardCount();
    this.getUserChartData();
    this.setupChartOptions();
    this.getTopRatingData();
  }

  setupChartOptions() {
    this.basicOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            color: '#F8FAF9',
          },
        },
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Months',
            color: '#F8FAF9',
          },
          ticks: {
            color: '#F8FAF9',
          },
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Count',
            color: '#F8FAF9',
          },
          ticks: {
            color: '#F8FAF9',
          },
        },
      },
    };
  }

  onTimeframeChange(event: any) {
    this.selectedButton = event.target.value;
    this.getUserChartData();
  }
  onframeChange(event: any) {
    this.selectedPeriod = event.target.value;
    this.getUserChartData();
  }

  getUserChartData() {
    this.ngxSpinnerService.show('globalSpinner');
    const type = {
      functionType: this.selectedButton,
      months: this.selectedPeriod,
    };
    this.dataTransferService.getChartData(type).subscribe(
      (res: any) => {
        console.log(res, 'chart Response');
        const data = res?.[0];
        this.ngxSpinnerService.hide('globalSpinner');

        if (this.selectedButton === 'users') {
          if (this.selectedPeriod === '3 months') {
            const quarterlyLabels = Object.keys(data.quarterlyUserCounts);
            const quarterlyData = Object.values(data.quarterlyUserCounts);

            this.basicData = {
              labels: quarterlyLabels,
              datasets: [
                {
                  label: 'Users (Quarterly)',
                  data: quarterlyData,
                  fill: false,
                  borderColor: '#94C11F',
                  tension: 0.4,
                },
              ],
            };
          } else if (this.selectedPeriod === 'all') {
            this.basicData = {
              labels: data.monthlyUserCounts.map(
                (item: { month: any }) => item.month
              ),
              datasets: [
                {
                  label: 'Users (Monthly)',
                  data: data.monthlyUserCounts.map(
                    (item: { count: any }) => item.count
                  ),
                  fill: false,
                  borderColor: '#94C11F',
                  tension: 0.4,
                },
              ],
            };
          }
        }
      },
      (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.log('error', error);
      }
    );
  }

  /* getDashboardCount() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getDashboardCount().subscribe({
      next: (value: any) => {
        const data = value?.data || {};
        if (this.isAdmin) {
          this.totalStoryTeller = data.totalStoryTeller || 0;
          this.uploadedStories = data.totalStories || 0;
          this.totalUser = data.totalAppUser || 0;
          this.mostHearingLanguage = data.mostHearingLanguage || 'N/A';
          this.mostPlayedGenere = data.mostPlayedGenere
            ? `${formatSecondsToHrMin(data.mostPlayedGenere.totalSeekTime)} (${data.mostPlayedGenere.genre})`
            : 'N/A';
          this.mostSubscribePlan = data.mostSubscribePlan || 'N/A';

          // Pie Chart
          this.userPieChartData = {
            labels: ['Active Users', 'Inactive Users', 'Trial Users'],
            datasets: [
              {
                data: [data.activeUsers || 0, data.inactiveUsers || 0, data.trialUsers || 0],
                backgroundColor: ['#42A5F5', '#66BB6A', '#FFA726'],
                hoverBackgroundColor: ['#64B5F6', '#81C784', '#FFB74D']
              }
            ]
          };

          this.userPieChartOptions = {
            responsive: true,
            plugins: {
              legend: {
                position: 'bottom'
              }
            }
          };
          // Bar Chart
          const barLabels: string[] = [];
          const barData: number[] = [];
          data.subscriptionChartData?.forEach((item: any) => {
            barLabels.push(item.sub_title);
            barData.push(item.user_count);
          });

          this.subscriptionBarChartData = {
            labels: barLabels,
            datasets: [
              {
                label: 'Subscriptions',
                backgroundColor: '#42A5F5',
                data: barData
              }
            ]
          };

          this.subscriptionBarChartOptions = {
            responsive: true,
            plugins: {
              legend: {
                display: true,
                position: 'top'
              }
            },
            scales: {
              x: {},
              y: {
                beginAtZero: true
              }
            }
          };
        }
    else {
      this.publishedStories = data.publishedStories || 0;
      this.uploadedStories = data.totalStories || 0;
      this.totalListingHour = formatSecondsToHrMin(data.totalSeekTime);
    }
        this.initializeCards();
    this.ngxSpinnerService.hide('globalSpinner');
  },
  error: (err) => {
        this.toastr.error(
    err.error?.message || 'Failed to load dashboard data'
  );
console.error(err);
this.ngxSpinnerService.hide('globalSpinner');
      },
complete: () => {
  this.ngxSpinnerService.hide('globalSpinner');
},
    });
  } */

  getDashboardCount() {
    this.ngxSpinnerService.show('globalSpinner');

    this.dataTransferService.getDashboardCount().subscribe({
      next: (value: any) => {
        const data = value?.data || {};

        this.isAdmin ? this.handleAdminDashboard(data) : this.handleNonAdminDashboard(data);

        this.initializeCards();
      },
      error: (err) => {
        this.toastr.error(err.error?.message || 'Failed to load dashboard data');
        console.error(err);
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  private handleAdminDashboard(data: any) {
    this.totalStoryTeller = data.totalStoryTeller || 0;
    this.uploadedStories = data.totalStories || 0;
    this.totalUser = data.totalAppUser || 0;
    this.mostHearingLanguage = data.mostHearingLanguage || 'N/A';

    this.mostPlayedGenere = data.mostPlayedGenere
      ? `${formatSecondsToHrMin(data.mostPlayedGenere.totalSeekTime)} (${data.mostPlayedGenere.genre})`
      : 'N/A';

    this.mostSubscribePlan = data.mostSubscribedPlan?.sub_title || 'N/A';

    this.setUserPieChart(data);
    this.setSubscriptionBarChart(data.subscriptionChartData || []);
  }

  private handleNonAdminDashboard(data: any) {
    this.publishedStories = data.publishedStories || 0;
    this.uploadedStories = data.totalStories || 0;
    this.totalListingHour = formatSecondsToHrMin(data.totalSeekTime || 0);
  }

  private setUserPieChart(data: any) {
    this.userPieChartData = {
      labels: ['Active Subscription', 'Inactive Users', 'Trial Subscription', 'Expired Subscription'],
      datasets: [
        {
          data: [data.activeUsers || 0, data.inactiveUsers || 0, data.trialUsers || 0, data.expiredUsers || 0],
          backgroundColor: ['#66BB6A', '#42A5F5', '#FFA726', '#EF5350'],
          hoverBackgroundColor: ['#81C784', '#64B5F6', '#FFB74D', '#E57373']
        }
      ]
    };

    this.userPieChartOptions = {
      responsive: true,
      maintainAspectRatio: false,

      plugins: {
        legend: {
          position: 'bottom',
          align: 'start',


          labels: {



            // Add generateLabels function to customize legend text
            generateLabels: function (chart: any) {
              const data = chart.data;
              if (data.labels.length && data.datasets.length) {
                return data.labels.map((label: string, i: number) => {
                  const meta = chart.getDatasetMeta(0);
                  const style = meta.controller.getStyle(i);
                  const value = data.datasets[0].data[i];

                  return {

                    text: `${label}: ${value}`,
                    fillStyle: style.backgroundColor,
                    strokeStyle: style.borderColor,
                    lineWidth: style.borderWidth,
                    hidden: !chart.getDataVisibility(i),
                    fontColor: "#6c757d",

                    // Extra data used for toggling the datasets
                    index: i
                  };
                });
              }
              return [];
            }
          }
        }
      }
    };
  }

  private setSubscriptionBarChart(chartData: any[]) {
    const barLabels = chartData.map((item) => item.sub_title);
    const barData = chartData.map((item) => item.user_count);

    this.subscriptionBarChartData = {
      labels: barLabels,
      datasets: [
        {
          label: 'Subscriptions',
          backgroundColor: '#42A5F5',
          data: barData
        }
      ]
    };

    this.subscriptionBarChartOptions = {
      responsive: true,
      // maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom'
        }
      },
      scales: {
        x: {},
        y: {
          beginAtZero: true
        }
      }
    };
  }


  isNumber(value: any): boolean {
    return typeof value === 'number';
  }

  isSmallScreen() {
    return window.innerWidth < 768;
  }

  isMediumScreen() {
    return window.innerWidth >= 768 && window.innerWidth <= 1400;
  }

  isLargeScreen() {
    return window.innerWidth > 1400;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.isSmallScreen();
    this.isMediumScreen();
    this.isLargeScreen();
  }

  getTopRatingData() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getTopRatingData().subscribe({
      next: (value: any) => {
        const data = value?.data || {};
        if (this.isAdmin) {
          //this.topStoryTellers = data;
          this.topStoryTellers = data.map((item: any) => ({
            ...item,
            formattedListeningTime: formatSecondsToHrMin(item.totalListeningHours),
          }));

        } else {
          this.storyBasedOnHours = data.TopThreeStoryBasedOnListeningHours;

          this.storyBasedOnRatings = data.TopThreeStoryBasedOnRating;
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(
          err.error?.message || 'Failed to load dashboard data'
        );
        console.error(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }
}
