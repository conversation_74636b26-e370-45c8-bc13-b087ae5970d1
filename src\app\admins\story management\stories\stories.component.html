<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">

        <!-- <div>
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div> -->

        <div class="row mb-5 head-Home">
            <div class="col-lg-3 position-relative">
                <app-search-box [searchTerm]="previousSearchTerm" (searchValue)="onSearch($event)"></app-search-box>
            </div>

        </div>

        <div class="row mb-4 head-Home d-flex align-items-end">
            <div class="col-lg-3 position-relative">
                <label class="form-label" for="filter">Uploaded by</label>
                <app-single-select-dropdown id="filter" (selectionChange)="onDropdownChange($event)" [isDefaultSelected]="true"  [options]="filters" [labelKey]="'title'"
                    [valueKey]="'id'"></app-single-select-dropdown>
            </div>

            <div class="col-lg-9 mb-2 mb-lg-0 stories-btns mt-small-2">
                <button *ngIf="isAdmin" type="button" class="btn btn-outline-primary mt-small-2 mr-3"
                    routerLink="/stories/requests">Requests</button>
                <button *ngIf="isAdmin" type="button" class="btn btn-outline-primary mt-small-2 mr-3"
                    routerLink="/stories/recommendations">Recommendations</button>
                <button type="button" class="btn btn-outline-primary mt-small-2"
                    routerLink="/stories/story">Add New Story</button>
            </div>

        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" [columns]="columns" [searchTerm]="searchTerm"
            [actionPermissions]="{  edit: true }" [data]="storyList" [storyLanguages]="languageData"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord"
            [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged" [module]="_tomodule"
            modalId="addSubscriptionPlanFormModal"></app-list-with-pagination>


    </div>
</app-sidebar>