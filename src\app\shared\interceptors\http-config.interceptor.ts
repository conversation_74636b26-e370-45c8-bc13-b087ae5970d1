import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { DataTransferService } from '../services/data-transfer.service';
@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {
  email:string = localStorage.getItem('logedInUserEmailId')||'';
  deviceId:string = localStorage.getItem('deviceId')||'';
  constructor(private router: Router, private toastr: ToastrService,
    private dataTransferService: DataTransferService,
  ) { }

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {

    const xAccessTtoken = localStorage.getItem('token');
    // Bypass for login requests
    if (request.url.includes('/pass-code-verify') || request.url.includes('email-verification')) {
      return next.handle(request);
    }
    let newHeaders = request.headers;
    const PortalType = 'ADMIN'
    if (xAccessTtoken) {
      newHeaders = newHeaders.append('Authorization', xAccessTtoken);
      // newHeaders = newHeaders.append('Portal-Type', PortalType);
    } else {
      if (this.email && this.deviceId) {
        this.dataTransferService.logout(this.email, this.deviceId);
      }
      localStorage.clear();
      this.router.navigate(['/login']);
    }
    const authReq = request.clone({ headers: newHeaders });
    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          if (this.email && this.deviceId) {
            this.dataTransferService.logout(this.email, this.deviceId).subscribe({
              next: () => {
                localStorage.clear();
                this.router.navigate(['']);
                this.toastr.error('Your session has expired. Please log in again.');
              },
              error: (err) => {
                console.error('Logout failed', err);
                this.toastr.error('Your session has expired. Please log out and log in again.');
              },
            });
          } else {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.error('Your session has expired. Please log in again.');
          }
        }
        return throwError(() => error);
      })
    );
  }
}
