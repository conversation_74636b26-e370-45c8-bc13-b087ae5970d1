.password-wrapper {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    top: 71%;
    right: 10px;
    transform: translateY(-50%);
    cursor: pointer;
}

// .content-wrapper {
//     background-image: url('../../assets/images/ELJUNTOBACKGROUND.jpg');
//     background-size: cover;
//     background-position: center center;
// }

.login-form {
    width: 580px !important;
    background: #1E1E1E;
    border: 1px solid #2D2D2D;
    border-radius: 36px;
    box-shadow: 2.5px 2.5px 3px 0px #101316, -2px -2px 3px 0px #393E44;
}



.login-form-btn {
    color: #fff;
    font-size: 1.125rem !important;
    background-color: #253943;
    border-color: #253943
}


.auth form .login-form-btn {
    padding: 10px 15px 10px 15px;
    line-height: 1.5;
}

label {
    font-size: 1rem !important;
}

@media (max-width: 768px) {
    .login-form {
        width: 80vw !important;
    }
}



