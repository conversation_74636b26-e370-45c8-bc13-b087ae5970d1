.brand-logo img {
  margin: auto;
  margin-right: 22px;
}


.navbar {
  display: flex;
  justify-content: center;
  align-items: center;
}

.menuTitle {
  font-size: 24px;
  font-weight: 500;
}

// .navbar-menu-wrapper {
//   background: #7C9C9A;
//   transition: width 0.25s ease;
//   color: #6C7383;
//   padding-right: 2.375rem;
//   padding-left: 2.375rem;
//   height: 60px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   border-bottom: 1px solid #253943;
// }

// .navbar .navbar-brand-wrapper {
//   border-right: 1px solid #253943;
// }

.sidebar {
  height: 100vh;
  background: #1E1E1E;
  font-weight: 400;
  font-size: 16px;
  // padding: 36px 28px 36px 28px;
  // width: 230px;
  border-right: 1px solid #2D2D2D;
  box-shadow: -2px -2px 3px #393e44, 2px 2px 3px #101316;
  overflow-y: hidden;
}

.sidebar .nav .nav-item.active-menu>.nav-link,
.sidebar .nav .nav-item .nav-link.active-menu {
  color: #94C11F !important;
  box-shadow: 1px 1px 1px #10131666 inset, -1px -1px 1px #393E44 inset;

}

.sidebar .nav .nav-item .nav-link.active-menu .menu-title {
  color: #94C11F !important;
}

.page-body-wrapper {
  max-height: 100vh;
}

.modal-open .sidebar-offcanvas {
  overflow: hidden;
}

.sidebar .nav .nav-item .nav-link {
  color: #F8FAF9;
  padding: 8px 10px;
  gap: 16px;
  border-radius: 8px;
  background: #1E1E1E;
  box-shadow: -2px -2px 3px #393e44, 2px 2px 3px #101316;
}

.nav-link.active-menu img {
  filter: none;
}

.active-menu {
  color: #94C11F !important;
  box-shadow: 1px 1px 1px 0px #10131666 inset, -1px -1px 1px 0px #393E44 inset, ;
  border-radius: 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px;
}

.sidebar .nav .nav-item:hover>.nav-link i,
.sidebar .nav .nav-item:hover>.nav-link .menu-title,
.sidebar .nav .nav-item:hover>.nav-link .menu-arrow {
  color: #94C11F !important;
}

.logOutModal {
  background-color: rgba(0, 0, 0, 0.5);
  color: black !important;
  font-size: 1rem;
}

.sidebar-icon {
  font-size: 20px;
  padding: 2px;
}

.submenu {
  list-style: none;
  border-bottom: 1px solid #2D2D2D;
  border-left: 1px solid #2D2D2D;
  border-right: 1px solid #2D2D2D;
  border-radius: 0 0 8px 8px;
}

.submenu .nav-item {
  margin: 5px 0;
}

.submenu .nav-item .nav-link {
  width: auto !important;
  height: Hug (44px)px;
  color: #F8FAF9;
  padding: 12px;
  border-radius: 0px !important;
  background: #1E1E1E;
  border-right: none !important;
  box-shadow: none !important;
}

.submenu .nav-item .nav-link.active-menu span {
  color: #94C11F !important;
}

.sidebar-btn-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

@media (max-width: 991px) {
  .navbar {
    justify-content: space-between;
  }

}