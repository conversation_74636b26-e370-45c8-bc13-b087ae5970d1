import {
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { FormGroup, FormBuilder, Validators, FormArray } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import {
  getBreadCrumbModules,
  getLoggedInUserId,
} from 'src/app/config/commonHelper';
import { ModuleTypes } from 'src/app/config/constants';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { Constants } from 'src/app/config/constants';
import { Router } from '@angular/router';
@Component({
  selector: 'app-select-class',
  templateUrl: './select-class.component.html',
  styleUrls: ['./select-class.component.scss']
})
export class SelectClassComponent implements OnInit {
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.SELECTCLASS);
  _tomodule: string = ModuleTypes.SELECTCLASS;
  menuTitle: string = 'Add School';
  classData= [ 
    { id:1,name:'Grade 1' },
    { id:2,name:'Grade 2' },
    { id:3,name:'Grade 3' },
    { id:4,name:'Grade 4' },
  ];
  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
    private router: Router
  ) { }

  ngOnInit(): void {
  }


  
}
