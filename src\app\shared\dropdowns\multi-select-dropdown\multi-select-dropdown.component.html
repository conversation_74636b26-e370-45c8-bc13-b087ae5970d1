<div class="custom-multiselect" [class.readonly]="readonly" (blur)="markAsTouched()">
  <div class="dropdown-trigger" [ngClass]="{'has-tags': selectedItems&&selectedItems.length > 0}"
    (click)="toggleDropdown()">
    <div class="selected-tags">
      <span class="tag" *ngFor="let item of selectedItems">
        {{ item[labelKey] }}
        <button *ngIf="!readonly" (click)="removeSelectedItem(item)" class="remove-tag-btn">&times;</button>
      </span>
    </div>
    <span class="placeholder" *ngIf="selectedItems.length === 0">{{ placeholder }}</span>
    <span *ngIf="!readonly" class="arrow-icon" [class.open]="showDropdown">&#9660;</span>
  </div>

  <div class="options-dropdown" *ngIf="showDropdown">
    <input *ngIf="options" type="text" [(ngModel)]="searchText" placeholder="Search..." class="search-box"
      (input)="filterOptions()" />

    <label *ngIf="!options">No data available</label>

    <div *ngFor="let item of filteredOptions" class="option-item">
      <input type="checkbox" style="cursor: pointer;" [id]="'option-' + item[valueKey]" [checked]="isSelected(item)"
        (change)="onCheckboxChange($event, item)" />
      <label [for]="'option-' + item[valueKey]">{{ item[labelKey] }}</label>
    </div>
  </div>
</div>