import { Injectable } from '@angular/core';

@Injectable()
export class Constants {
    public static readonly limit: number = 10;
    public static readonly offset: number = 1;
    public static readonly maxNumber: number = 2147483647;    
    public static readonly deviceId: string = 'ADMIN';
    public static readonly s3BaseUrl:string='https://kathamehfilbucket.s3.ap-south-1.amazonaws.com/audios/'
    public static readonly defaulProfilePicture='./assets/images/Profile_2.png'

}

export class ModuleTypes {
    public static readonly SUBSCRIPTIONPLANLIST: string = "SUBSCRIPTIONPLANLIST";
    public static readonly APPUSERS: string = "APPUSERS";
    public static readonly PORTALUSERS: string = "PORTALUSERS";
    public static readonly STORIES: string = "STORIES";
    public static readonly ADDSTORY: string = "ADDSTORY";
    public static readonly RECOMMENDATION: string = "RECOMMENDATION";
    public static readonly REQUESTS: string = "REQUESTS";
    public static readonly SCHOOLLIST: string = "SCHOOLLIST";
    public static readonly ADDEDITSCHOOL: string = "ADDEDITSCHOOL";
    public static readonly SELECTCLASS: string = "SELECTCLASS";
    public static readonly PLAYLISTS: string = "PLAYLISTS";
    public static readonly ADDPLAYLIST: string = "ADDPLAYLIST";


}

export class PlaylistTypes {
    public static readonly PUBLIC: string = "PUBLIC";
    public static readonly ORGANIZATION: string = "ORGANIZATION";
    public static readonly CUSTOM: string = "CUSTOM";
}
export class OrgTypes {
    public static readonly SCHOOL: string = "SCHOOL";
}

export class UserTypes {
    public static readonly ADMIN: string = "ADMIN";
    public static readonly AUTHOR: string = "AUTHOR";
    public static readonly APP: string = "USER";
    public static readonly STORYTELLER: string = "STORYTELLER";
}

export class StoryStatus {
    public static readonly PUBLISHED:string='PUBLISHED';
    public static readonly DRAFT:string='DRAFT';
    public static readonly REJECTED:string='REJECTED';
}