import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { OrgnizationService } from 'src/app/shared/services/orgnization.service';

import { Observable } from 'rxjs';
import { enrichStoryData, getBreadCrumbModules } from 'src/app/config/commonHelper';
import { Constants, ModuleTypes, PlaylistTypes, StoryStatus } from 'src/app/config/constants';
import { PlaylistService } from 'src/app/shared/services/playlist.service';
@Component({
  selector: 'app-add-new-playlist',
  templateUrl: './add-new-playlist.component.html',
  styleUrls: ['./add-new-playlist.component.scss']
})
export class AddNewPlaylistComponent implements OnInit, OnDestroy {
  totalNumberOfRecords: number = 0;
  offset: number = Constants.offset;
  limit: number = Constants.limit;
  offset2: number = 1;
  limit2: number = 10;
  searchTerm = '';
  storyList: any[] = [];
  addNewPlaylistForm: FormGroup;
  menuTitle: string = 'Playlist Management';
  _tomodule: string = ModuleTypes.ADDSTORY;
  isEdit: boolean = false;
  params: any;
  languageData: any;
  genresData: any;
  ageGroupData: any;
  isEditing: boolean = false;
  breadCrumbModules: ({ label: string; path: string; isActive?: undefined; } | { label: string; path: string; isActive: boolean; })[];
  maxNumber = Constants.maxNumber;
  columns: any[] = [
    { title: 'Name of the story', dataKey: 'stTitle' },
    // { title: 'Author Name', dataKey: 'authorName' },
    { title: 'Genre', dataKey: 'stGenreName' },
    { title: 'Age group', dataKey: 'ageGroup' },
    { title: 'Languages', dataKey: 'sfLanguageName' },
  ];
  storyIds: any[];
  playlistStories: any[] = [];
  selectedGenre: any;
  selectedPlaylistId: any;
  playlistData: any;
  // School context properties
  schoolContext: any;
  isFromSchoolManagement: boolean = false;
  schoolId: string;
  schoolName: string;

  // Class dropdown properties
  departmentsData: any[] = [];
  isLoadingDepartments: boolean = false;
  selectedClassId: string = '';
  selectedClassName: string = '';

  // Available playlists for selected class
  availablePlaylists: any[] = [];
  isLoadingPlaylists: boolean = false;
  showAvailablePlaylists: boolean = false;
  constructor(
    private readonly toastr: ToastrService,
    private readonly ngxSpinnerService: NgxSpinnerService,
    private readonly dataTransferService: DataTransferService,
    private readonly plasylistService: PlaylistService,
    private readonly formBuilder: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly orgnaizationService: OrgnizationService,
  ) {

    this.addNewPlaylistForm = this.formBuilder.group({
      playlistName: ['', Validators.required],
      genreId: ['', Validators.required],
      /* description: [''],
      tags: ['', Validators.required],
      ageGroupId: ['', Validators.required],*/
      dp: [null],
      playlistType: PlaylistTypes.PUBLIC,
      playlistStatus: StoryStatus.PUBLISHED,
      storyIds: ['', Validators.required],
      classId: [''] // Add class selection (required for school context)
    });

  }

  ngOnInit(): void {
    this.params = history?.state;

    // Handle school context from navigation state
    if (this.params?.isFromSchoolManagement) {
      this.isFromSchoolManagement = true;
      this.schoolContext = this.params.schoolContext;
      this.schoolId = this.params.schoolId;
      this.schoolName = this.params.schoolName;

      // Update menu title to show school context
      this.menuTitle = `Add Playlist for ${this.schoolName}`;

      // Set playlistType to ORGANIZATION for school context
      this.addNewPlaylistForm.get('playlistType')?.patchValue(PlaylistTypes.ORGANIZATION);

      // Make class selection required for school context
      this.addNewPlaylistForm.get('classId')?.setValidators([Validators.required]);
      this.addNewPlaylistForm.get('classId')?.updateValueAndValidity();

      // Load classes for the school
      this.loadDepartments();
    }

    this.selectedPlaylistId = this.params?.data?.plId ?? localStorage.getItem('selectedPlaylistId') ?? '';
    if (this.selectedPlaylistId) {
      this.isEdit = true;
      localStorage.setItem('selectedPlaylistId', this.selectedPlaylistId);
    }
    this.breadCrumbModules = getBreadCrumbModules(ModuleTypes.ADDPLAYLIST, {
      isEdit: this.isEdit,
      searchTerm: this.searchTerm
    });
    this.getAllGenres();
    if (this.isEdit) {
      this.getPlaylistById();
    } else {
      this.loadData();
    }
    /* Promise.all([
      this.getAllLanguages(),
      this.getAllAgeGroup(),
      this.getAllGenres(),
      this.loadData()
    ]).then(() => {
      if (this.isEdit) {
        this.getPlaylistById();
      }
    }); */
  }

  ngOnDestroy(): void {
    localStorage.removeItem('selectedPlaylistId');
  }


  getAllLanguages() {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllLanguages(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.languageData = value.data;
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getAllGenres() {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllGenres(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.genresData = value?.data;
          this.cdr.detectChanges();
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getAllAgeGroup() {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllAgeGroup(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.ageGroupData = value?.data;
          this.cdr.detectChanges();
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.loadData();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.loadData();
  };

  /* private enrichStoryData(stories: any[]): any[] {
    return stories?.map((story: any) => {
      const sfLanguageName = Array.from(
        new Set(story?.storyFiles.map((file: any) => file.sfLanguageName))
      );

      const ageGroup = story?.stAgeGroupName?.map((age: any) => age?.sm_entityname);

      return {
        ...story,
        sfLanguageName,
        ageGroup,
      };
    }) || [];
  } */


  getPlaylistById() {
    this.ngxSpinnerService.show('globalSpinner');
    this.plasylistService.getPlaylistById(this.selectedPlaylistId).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.playlistData = value?.data;
          //this.playlistStories = this.playlistData.story;
          this.playlistStories = enrichStoryData(this.playlistData.story);//this.enrichStoryData(this.playlistData.story);
          this.addNewPlaylistForm.patchValue({
            ... this.playlistStories,
            playlistName: this.playlistData.title,
            genreId: this.playlistData.genreId
          });
          this.cdr.detectChanges();
          this.loadData();
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getStoryList(): Promise<any> {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset - 1,
      roleId: '',
    };

    this.ngxSpinnerService.show('globalSpinner');

    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllStories(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.totalNumberOfRecords = value.count;
            /* const storyData = value.data?.map((story: any) => {
              const sfLanguageName = Array.from(
                new Set(story?.storyFiles.map((file: any) => file.sfLanguageName))
              );
              const ageGroup = story.stAgeGroupName?.map((ageGroup: any) => ageGroup?.sm_entityname)
              return {
                ...story,
                sfLanguageName,
                ageGroup
              };
            }); */
            this.storyList = enrichStoryData(value.data);
            resolve(this.storyList);
          } else {
            resolve([]);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
          reject(err);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    });
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.loadData();
  }
  private generateStoryIdMap(): Record<string, string> {
    return this.playlistStories?.reduce((acc: Record<string, string>, story: any, index: number) => {
      acc[`${index + 1}`] = story?.stId;
      return acc;
    }, {}) || {};
  }

  async loadData() {
    try {
      const storyList = await this.getStoryList();

      const storyIdMap = this.generateStoryIdMap();
      this.addNewPlaylistForm.get('storyIds')?.patchValue(storyIdMap);

      const selectedStoryIds = Object.values(storyIdMap);

      this.storyList = storyList?.map((story: any) => ({
        ...story,
        isAddBtn: !selectedStoryIds.includes(story?.stId),
      }));

    } catch (error) {
      console.error("Error loading story data", error);
    }
  }

  addStoryInPlaylist(event: { modalId: string; isEdit: boolean; data?: any }) {
    this.playlistStories.push(event.data);
    this.loadData();
  }

  removeFromPlaylist(index: number): void {
    this.playlistStories.splice(index, 1);
    this.loadData();
  }

  pagelimit2(limit2: number) {
    this.offset2 = 1;
    this.limit2 = limit2;
  };

  pageChange2(event: number) {
    this.offset2 = event;
  }

  onSubmit() {
    this.addNewPlaylistForm.markAllAsTouched();

    if (!this.isFormValid()) return;

    this.ngxSpinnerService.show('globalSpinner');
    const payload = this.isEdit ? this.getUpdatePayload() : this.getCreatePayload();
    const request$ = this.isEdit
      ? this.plasylistService.updatePlaylist(payload)
      : this.plasylistService.addPlaylist(payload);

    this.handlePlaylistRequest(request$);
  }

  private isFormValid(): boolean {
    const storyIds = this.addNewPlaylistForm.get('storyIds');
    const classId = this.addNewPlaylistForm.get('classId');

    // Check if no stories are added to the playlist
    if (this.playlistStories.length === 0) {
      this.toastr.error("Please add at least one story to the playlist");
      return false;
    }

    // Validate class selection for school context
    if (this.isFromSchoolManagement && classId?.invalid) {
      this.toastr.error("Please select a class for the playlist");
      return false;
    }

    if (this.addNewPlaylistForm.invalid) {
      this.toastr.error("Please fill all the required fields correctly");
      return false;
    }

    return true;
  }

  private getCreatePayload() {
    const formValue = { ...this.addNewPlaylistForm.value };

    // For school context, modify the payload structure
    if (this.isFromSchoolManagement) {
      // Set playlistType to ORGANIZATION for school playlists
      formValue.playlistType = PlaylistTypes.ORGANIZATION;

      // Add entityId with the selected deptId (class ID)
      formValue.entityId = this.selectedClassId;

      // Remove classId from payload as it's not needed
      delete formValue.classId;

      console.log('School playlist payload:', {
        playlistType: formValue.playlistType,
        entityId: formValue.entityId,
        selectedClassId: this.selectedClassId,
        schoolId: this.schoolId
      });
    }

    return formValue;
  }

  private getUpdatePayload() {
    const formValue = { ...this.addNewPlaylistForm.value };

    // For school context, modify the payload structure
    if (this.isFromSchoolManagement) {
      // Set playlistType to ORGANIZATION for school playlists
      formValue.playlistType = PlaylistTypes.ORGANIZATION;

      // Add entityId with the selected deptId (class ID)
      formValue.entityId = this.selectedClassId;

      // Remove classId from payload as it's not needed
      delete formValue.classId;
    }

    return {
      ...formValue,
      plId: this.playlistData.plId,
      entityId: this.isFromSchoolManagement ? this.selectedClassId : this.playlistData.entityId,
      title: this.addNewPlaylistForm.get('playlistName')?.value
    };
  }

  private handlePlaylistRequest(request$: Observable<any>) {
    request$.subscribe({
      next: (response) => {
        this.toastr.success(response.message);
        // Navigate back to appropriate location based on context
        if (this.isFromSchoolManagement) {
          this.router.navigate(['/school-list'], {
            state: {
              message: `Playlist successfully ${this.isEdit ? 'updated' : 'created'} for ${this.schoolName}`
            }
          });
        } else {
          this.router.navigate(['/playlists']);
        }
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || 'Error Occurred');
        console.error(err);
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  onGenreSelected(genre: any) {
    this.selectedGenre = genre;
    this.addNewPlaylistForm.get('dp')?.patchValue(this.selectedGenre?.dp);
    this.addNewPlaylistForm.get('genreId')?.patchValue(this.selectedGenre?.genId);
  }
  onAgeSelected(ageGroup: any) {
    this.addNewPlaylistForm.get('ageGroupId')?.patchValue(ageGroup?.agId);
  }

  drop(event: CdkDragDrop<any[]>) {
    moveItemInArray(this.playlistStories, event.previousIndex, event.currentIndex);

    const storyIdMap = this.generateStoryIdMap();
    this.addNewPlaylistForm.get('storyIds')?.patchValue(storyIdMap);
  }

  // Load departments/classes for the school
  private loadDepartments(): void {
    if (!this.schoolId) {
      console.log('No schoolId available, skipping department load');
      return;
    }

    this.isLoadingDepartments = true;
    const params = {
      limit: 1000, // High limit to get all classes for the school
      offset: 0,
      userId: '', // Send empty userId as specified
      orgId: this.schoolId.toString()
    };

    console.log('Loading departments for playlist with params:', params);
    this.orgnaizationService.getAllDepartments(params).subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.departmentsData = response.data;
          console.log(`Classes loaded for playlist: ${this.departmentsData.length} classes found`, this.departmentsData);
        } else {
          this.departmentsData = [];
        }
      },
      error: (error) => {
        console.error('Error loading departments for playlist:', error);
        this.toastr.error('Failed to load classes');
        this.departmentsData = [];
      },
      complete: () => {
        this.isLoadingDepartments = false;
      }
    });
  }

  // Handle class selection
  onClassSelected(classData: any): void {
    this.selectedClassId = classData?.deptId || '';
    this.selectedClassName = classData?.deptName || '';
    this.addNewPlaylistForm.get('classId')?.patchValue(this.selectedClassId);

    console.log('Class selected for playlist:', {
      classId: this.selectedClassId,
      className: this.selectedClassName
    });

    // Update menu title to include class name
    if (this.selectedClassName && this.isFromSchoolManagement) {
      this.menuTitle = `${this.isEdit ? 'Edit' : 'Add'} Playlist for ${this.selectedClassName}`;
    }

    // Update playlist title suggestion to include class name when switching classes
    if (this.selectedClassName && this.isFromSchoolManagement && !this.isEdit) {
      this.updatePlaylistTitleForClass();
    }

    // Clear previous class data and reset genre when switching classes (only for new playlists)
    if (!this.isEdit) {
      console.log('Clearing previous class data before loading new class playlists');
      this.clearPreviousClassData();
    }

    // Load available playlists for the selected class
    if (this.selectedClassId) {
      this.loadAvailablePlaylistsForClass(this.selectedClassId);
    } else {
      // If no class selected, clear everything
      this.clearClassRelatedData();
    }

    // Optionally filter stories based on selected class
    // this.loadData(); // Reload stories if needed
  }

  // Load available playlists for the selected class and populate playlist stories
  private loadAvailablePlaylistsForClass(deptId: string): void {
    this.isLoadingPlaylists = true;
    this.showAvailablePlaylists = false;
    this.availablePlaylists = [];

    console.log('Loading playlists for class with deptId:', deptId);

    this.plasylistService.getPlaylistsByEntityId(deptId).subscribe({
      next: (response: any) => {
        console.log('Available playlists response:', response);
        if (response && response.data) {
          this.availablePlaylists = response.data;
          this.showAvailablePlaylists = true;

          // Extract all stories from all playlists and populate playlistStories
          this.populatePlaylistStoriesFromAvailablePlaylists();

          // Auto-populate genre from first playlist (only for new playlist creation, not editing)
          if (!this.isEdit && this.availablePlaylists.length > 0) {
            this.autoPopulateGenreFromFirstPlaylist();
          }

          console.log(`Found ${this.availablePlaylists.length} playlists for class ${this.selectedClassName}`);
        } else {
          this.availablePlaylists = [];
          this.showAvailablePlaylists = true;
          this.playlistStories = [];

          // Clear genre selection when no playlists are found (only for new playlist creation)
          if (!this.isEdit) {
            this.clearGenreSelection();
          }

          console.log('No playlists found for this class');
        }
      },
      error: (error) => {
        console.error('Error loading playlists for class:', error);
        this.toastr.error('Failed to load playlists for this class');
        this.availablePlaylists = [];
        this.showAvailablePlaylists = true;
        this.playlistStories = []; // Clear existing stories on error

        // Clear genre selection on error (only for new playlist creation)
        if (!this.isEdit) {
          this.clearGenreSelection();
        }
      },
      complete: () => {
        this.isLoadingPlaylists = false;
        // Reload story data to update the "Add" button states
        this.loadData();
      }
    });
  }

  // Populate playlistStories with all stories from available playlists
  private populatePlaylistStoriesFromAvailablePlaylists(): void {
    const allStories: any[] = [];
    const storyIds = new Set(); // To avoid duplicates

    this.availablePlaylists.forEach(playlist => {
      if (playlist.story && Array.isArray(playlist.story)) {
        playlist.story.forEach((story: any) => {
          if (!storyIds.has(story.stId)) {
            storyIds.add(story.stId);
            // Enrich story data to match the expected format
            const enrichedStory = {
              ...story,
              sfLanguageName: Array.from(
                new Set(story?.storyFiles?.map((file: any) => file.sfLanguageName) || [])
              ),
              ageGroup: story?.stAgeGroupName?.map((age: any) => age?.sm_entityname) || []
            };
            allStories.push(enrichedStory);
          }
        });
      }
    });

    this.playlistStories = allStories;
    console.log(`Populated ${this.playlistStories.length} stories from available playlists`);
  }

  // Auto-populate genre from the first playlist when loading existing playlists for a class
  private autoPopulateGenreFromFirstPlaylist(): void {
    console.log('autoPopulateGenreFromFirstPlaylist called');

    if (!this.availablePlaylists || this.availablePlaylists.length === 0) {
      console.log('No available playlists to auto-populate genre from');
      return;
    }

    const firstPlaylist = this.availablePlaylists[0];
    const genreId = firstPlaylist.genreId;

    console.log('First playlist:', firstPlaylist);
    console.log('Genre ID from first playlist:', genreId);

    if (genreId) {
      console.log('Auto-populating genre from first playlist, genreId:', genreId);

      // Find the genre object from genresData using the genreId
      const matchingGenre = this.getGenreById(genreId);

      if (matchingGenre) {
        console.log('Found matching genre:', matchingGenre);
        // Use the existing onGenreSelected method to populate the genre
        this.onGenreSelected(matchingGenre);
        console.log('Auto-populated genre:', matchingGenre.title);

        // Trigger change detection after auto-population
        this.cdr.detectChanges();
      } else {
        console.log('No matching genre found for genreId:', genreId);
        console.log('Available genres:', this.genresData);
      }
    } else {
      console.log('No genreId found in first playlist');
    }
  }

  // Helper method to find genre by ID from the loaded genres data
  private getGenreById(genreId: number): any {
    if (!this.genresData || !Array.isArray(this.genresData)) {
      console.log('No genres data available for lookup');
      return null;
    }

    return this.genresData.find(genre => genre.genId === genreId) || null;
  }

  // Update playlist title to include the new class name when switching classes
  private updatePlaylistTitleForClass(): void {
    if (!this.selectedClassName) {
      return;
    }

    const currentTitle = this.addNewPlaylistForm.get('playlistName')?.value || '';

    // If the title is empty or only contains a previous class name prefix, update it
    if (!currentTitle || this.isPreviousClassPrefix(currentTitle)) {
      const suggestedTitle = `${this.selectedClassName} `;
      this.addNewPlaylistForm.get('playlistName')?.patchValue(suggestedTitle);
      console.log('Updated playlist title for new class:', suggestedTitle);
    }
  }

  // Check if the current title appears to be a previous class name prefix
  private isPreviousClassPrefix(title: string): boolean {
    // Simple heuristic: if title ends with a space and doesn't contain much content
    // This could be enhanced based on specific requirements
    return title.trim().split(' ').length <= 2 && title.endsWith(' ');
  }

  // Clear previous class-related data when switching classes
  private clearPreviousClassData(): void {
    // Clear available playlists and stories from previous class
    this.availablePlaylists = [];
    this.showAvailablePlaylists = false;
    this.playlistStories = [];

    // Also clear genre selection when switching classes
    this.clearGenreSelection();

    console.log('Cleared previous class data and genre selection for new class selection');
  }

  // Clear genre selection
  private clearGenreSelection(): void {
    console.log('Clearing genre selection...');

    // Clear the selected genre object
    this.selectedGenre = null;

    // Clear form control values using multiple approaches to ensure it works
    const genreControl = this.addNewPlaylistForm.get('genreId');
    const dpControl = this.addNewPlaylistForm.get('dp');

    if (genreControl) {
      genreControl.setValue('');
      genreControl.patchValue('');
      genreControl.reset();
      genreControl.markAsUntouched();
      genreControl.markAsPristine();
    }

    if (dpControl) {
      dpControl.setValue(null);
      dpControl.patchValue(null);
    }

    // Force form to update and trigger change detection
    this.addNewPlaylistForm.updateValueAndValidity();
    this.cdr.detectChanges();

    console.log('Genre selection cleared. Form genreId value:', this.addNewPlaylistForm.get('genreId')?.value);
  }

  // Clear all class-related data when no class is selected
  private clearClassRelatedData(): void {
    this.availablePlaylists = [];
    this.showAvailablePlaylists = false;
    this.playlistStories = [];

    if (!this.isEdit) {
      this.clearGenreSelection();
    }

    console.log('Cleared all class-related data');
  }

}
