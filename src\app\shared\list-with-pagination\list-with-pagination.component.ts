import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output
} from '@angular/core';
import { Router } from '@angular/router';
import { Constants, ModuleTypes } from 'src/app/config/constants';
declare var $: any;
@Component({
  selector: 'app-list-with-pagination',
  templateUrl: './list-with-pagination.component.html',
  styleUrls: ['./list-with-pagination.component.scss'],
})
export class ListWithPaginationComponent implements OnInit {
  @Input() idKey: string;
  @Input() columns: any[];
  @Input() modalTitle: string;
  @Input() actionPermissions: any;
  @Input() data: any[];
  @Input() viewPageLink: string;
  @Input() onCurrentPageChanged: Function;
  @Input() totalNumberOfRecords: number;
  @Input() onPageSizeChanged: Function;
  @Input() onDeleteRecord: Function;
  @Input() onSwitchUpdate: Function;
  @Input() onUpdateBookRequestStatus: Function;
  @Input() searchTerm: string;
  @Input() modalId: string;
  @Input() module: string;
  @Input() cardTitle: string = '';
  @Input() showPagination: boolean = true;
  @Output() showOtherModal: EventEmitter<{
    modalId: string;
    isEdit: boolean;
    data?: any;
  }> = new EventEmitter();
  selectedDeleteId: any;
  columnName: any;
  defProfilePicture = Constants.defaulProfilePicture;
  selectedRequestData: any;
  @Input() perPageItems: number;
  @Input() p: number;
  switchBtnData: any;
  @Input() paginatioNo: number = 1;
  @Input() storyLanguages: any[];
  @Input() activeLabel: string = 'ACTIVE'; // Default text for active
  @Input() inactiveLabel: string = 'INACTIVE'; // Default text for inactive
  // storyLanguages=[
  //   {
  //     id:1,
  //     language:'Hindi'
  //   },
  //   {
  //     id:2,
  //     language:'Marathi'
  //   },
  //   {
  //     id:3,
  //     language:'English'
  //   }
  // ]
  commonModalData: any = '';
  commonModalHeader: string;

  constructor(private router: Router, private cdr: ChangeDetectorRef) { }

  ngOnInit(): void {
    console.log("Data", this.data);

  }

  // p: number = 0;
  // perPageItems: number = 10;
  selectableColumIndex: number = 0;
  tokenInfo: any = [];
  roleListArray: any[];
  selectedRow: any;

  handlePageSizeChange(value: number) {
    this.onPageSizeChanged(value, this.paginatioNo);
  }

  //   ngOnChanges(changes: SimpleChanges): void {
  //     console.log("this.numberOfRecords",this.totalNumberOfRecords);
  //     if (changes.activeTab && changes.activeTab.currentValue !== changes.activeTab.previousValue) {
  //         this.p = 0;
  //         // this.perPageItems = 10;
  //         // this.handlePageSizeChange(10);
  //     }

  //     if (changes?.totalNumberOfRecords && changes?.totalNumberOfRecords?.currentValue <10) {
  //         this.p = 0;
  //         // this.perPageItems = 10;
  //     }
  // }

  pageChange(event: number) {
    this.p = event;
    this.onCurrentPageChanged(this.p);
  }

  deleteRecord() {
    this.onDeleteRecord(this.selectedDeleteId);
  }

  openRecord(mode: string, element: any) { }

  showMembersList(bookClubId: any) {
    /* const queryParams = {
      bookClubId: bookClubId
    } */
    this.router.navigate([`/clubs/members`], {
      state: { id: bookClubId, view: true, module: this.module },
    });
  }
  // getStarsArray(rating: number) {
  //   const totalStars = 5;
  //   return new Array(totalStars).fill(0).map((_, index) => index < rating);
  // }

  updateBookRequestStatus() {
    this.onUpdateBookRequestStatus(this.selectedRequestData);
  }

  showModal(modalId: string, id?: any) {
    if (id) {
      this.selectedDeleteId = id;
    }

    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

  showCommonModal(modalId: string, columnName: string, data: any) {
    if (columnName === 'sfLanguageName') {
      const commonModalHeader = 'Languages the story is available in :';
      if (this.module == ModuleTypes.RECOMMENDATION) {
        const postData = {
          ModalHeader: commonModalHeader,
          ModalBody: data,
        };
        this.triggerOpenModal('languageModal', false, postData);
        return;
      } else {
        this.commonModalHeader = commonModalHeader;
        this.commonModalData = data.join(', ');
      }
    } else if (columnName === 'ageGroup') {
      const commonModalHeader = 'Age group the story is suitable for :';
      if (this.module == ModuleTypes.RECOMMENDATION) {
        const postData = {
          ModalHeader: commonModalHeader,
          ModalBody: data,
        };
        this.triggerOpenModal('languageModal', false, postData);
        return;
      } else {
        this.commonModalHeader = commonModalHeader;
        this.commonModalData = data.join(', ');
      }
    } else if (columnName === 'tags') {
      this.commonModalHeader = 'Tags associated with this story :';
      this.commonModalData = data.stTags ? data.stTags : 'No tags available';
    }
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'block';
    }
  }

  // getStarsArray(rating: number): string[] {
  //   const totalStars = 5;
  //   const stars = [];

  //   for (let i = 0; i < totalStars; i++) {
  //     if (i < Math.floor(rating)) {
  //       stars.push('full');
  //     } else if (i < rating) {
  //       stars.push('half');
  //     } else {
  //       stars.push('empty');
  //     }
  //   }
  //   return stars;
  // }

  onEditClick(modalId: string, data: any) {
    if (this.module == ModuleTypes.STORIES) {
      this.router.navigate([`/stories/story`], {
        state: {
          isEdit: true,
          data: data,
          searchTerm: this.searchTerm,
          isFromRequestComp: false,
        },
      });
    } else if (this.module === ModuleTypes.SCHOOLLIST) {
      // Navigate without orgId in URL, only use navigation state
      this.router.navigate([`/school-list/school`], {
        state: {
          isEdit: true,
          data: data,
        },
      });
    } else if (this.module === ModuleTypes.PLAYLISTS) {
      this.router.navigate([`/playlists/playlist`], {
        state: {
          isEdit: true,
          data: data,
          searchTerm: this.searchTerm,
        },
      });
    }
    else {
      this.triggerOpenModal(modalId, true, data);
    }
  }

  triggerOpenModal(modalId: string, isEdit: boolean, data?: any) {
    this.showOtherModal.emit({ modalId, isEdit, data });
  }

  onSwitchClick(event: Event, data: any) {
    event.preventDefault();
    event.stopPropagation();
    this.switchBtnData = data;
    this.showModal('lockUnlockModal');
  }

  onCheckboxChange(event: Event) {
    event.preventDefault();
    event.stopPropagation();
  }

  onSwitchModalClick() {
    this.onSwitchUpdate(this.switchBtnData);
  }

  // getLanguagesName(languageIds: any[]):any{
  //   return languageIds.map((id:any)=>this.storyLanguages?.find(lang=>lang?.laId==id)?.laTitle);
  // }

  onRowClick(element: any) {
    if (this.module == ModuleTypes.REQUESTS) {
      this.router.navigate([`/stories/story`], {
        state: { isFromRequestComp: true, element: element },
      });
    } else {
      return;
    }
  }

  verifyIsChecked(data: any): boolean | undefined {
    if (this.module === ModuleTypes.APPUSERS) {
      return data?.userStatus === 'LOCKED' ? false : true;
    }
    return undefined;
  }

  onManagePlaylistClick(schoolData: any) {
    // Navigate to add playlist with school context
    this.router.navigate(['/playlists/playlist'], {
      state: {
        schoolContext: schoolData,
        isFromSchoolManagement: true,
        schoolId: schoolData.orgId || schoolData.id,
        schoolName: schoolData.orgTitle || schoolData.name
      }
    });
  }


}
