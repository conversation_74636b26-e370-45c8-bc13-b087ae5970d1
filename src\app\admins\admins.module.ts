import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { AdminsRoutingModule } from './admins-routing.module';
import { AdminsComponent } from './admins.component';
import { SidebarModule } from '../shared/sidebar/sidebar.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { CamelCasePipe } from './camel-case-admin.pipe';
import { ListWithPaginationComponent } from '../shared/list-with-pagination/list-with-pagination.component';
import { InputWrapperComponent } from '../shared/input-wrapper/input-wrapper.component';
import { BreadcrumbComponent } from '../shared/breadcrumb/breadcrumb.component';
import { SearchBoxComponent } from '../shared/search-box/search-box.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { PlanListComponent } from './subscription plans managment/plan-list/plan-list.component';
import { AppUsersComponent } from './users/app-users/app-users.component';
import { StoriesComponent } from './story management/stories/stories.component';
import { AddStoryComponent } from './story management/add-story/add-story.component';
import { PortalUsersComponent } from './users/portal-users/portal-users.component';
import { CategoriesComponent } from './category management/categories/categories.component';
import { NgxMaskModule } from 'ngx-mask';
import { MultiSelectDropdownComponent } from '../shared/dropdowns/multi-select-dropdown/multi-select-dropdown.component';
import { SingleSelectDropdownComponent } from '../shared/dropdowns/single-select-dropdown/single-select-dropdown.component';
import { RecommendationComponent } from './story management/recommendation/recommendation.component';
import { RequestsComponent } from './story management/requests/requests.component';
import { SchoolsComponent } from './school managment/schools/schools.component';
import { AddEditSchoolComponent } from './school managment/add-edit-school/add-edit-school.component';
import { SelectClassComponent } from './school managment/select-class/select-class.component';
import { ManagePlaylistComponent } from './school managment/manage-playlist/manage-playlist.component';
import { PlaylistsComponent } from './playlists management/playlists/playlists.component';
import { AddNewPlaylistComponent } from './playlists management/add-new-playlist/add-new-playlist.component';
@NgModule({
  declarations: [
    AdminsComponent,
    ListWithPaginationComponent,
    CamelCasePipe,
    InputWrapperComponent,
    BreadcrumbComponent,
    SearchBoxComponent,
    PlanListComponent,
    AppUsersComponent,
    StoriesComponent,
    AddStoryComponent,
    PortalUsersComponent,
    CategoriesComponent,
    MultiSelectDropdownComponent,
    SingleSelectDropdownComponent,
    RecommendationComponent,
    RequestsComponent,
    SchoolsComponent,
    AddEditSchoolComponent,
    SelectClassComponent,
    ManagePlaylistComponent,
    PlaylistsComponent,
    AddNewPlaylistComponent
  ],
  imports: [
    CommonModule,
    DragDropModule,
    AdminsRoutingModule,
    SidebarModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgbTooltipModule,
    Ng2SearchPipeModule,
    NgSelectModule,
    NgxMaskModule.forRoot()
    ],
  exports: [
    ListWithPaginationComponent,
    InputWrapperComponent,
    BreadcrumbComponent,
    SearchBoxComponent,
  ]

})
export class AdminsModule { }
