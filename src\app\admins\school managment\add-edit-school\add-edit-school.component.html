<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div class="mt-0">
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div>


        <div class="add-new-school">
            <span class="heading-text">{{isEdit?'Edit School':'Add School'}}</span>
            <form class="forms-sample mt-2" [formGroup]="addNewSchoolForm">
                <div class="row mb-5 katha-card py-3 mx-0 px-0">
                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgTitle" id="orgTitle" label="School Name" type="text"
                            [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgTitle')?.invalid && addNewSchoolForm.get('orgTitle')?.touched"
                            [errorMessage]="'School name is required.'">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgAddress" id="orgAddress" label="Address" type="text"
                            [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgAddress')?.invalid && addNewSchoolForm.get('orgAddress')?.touched"
                            [errorMessage]="'Address is required.'">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgEmail" id="orgEmail" label="Email" type="email"
                            [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgEmail')?.invalid && addNewSchoolForm.get('orgEmail')?.touched"
                            [errorMessage]="'Valid email is required.'">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgPhone" id="orgPhone" label="Phone Number" type="text"
                            [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgPhone')?.invalid && addNewSchoolForm.get('orgPhone')?.touched"
                            [errorMessage]="'Valid phone number is required.'" [maxLength]="10" [useNumberMask]="true">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgSpoc1FullName" id="orgSpoc1FullName"
                            label="Contact Person Name" type="text" [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgSpoc1FullName')?.invalid && addNewSchoolForm.get('orgSpoc1FullName')?.touched"
                            [errorMessage]="'Contact person name is required.'">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgSpoc1Email" id="orgSpoc1Email"
                            label="Contact Person Email" type="email" [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgSpoc1Email')?.invalid && addNewSchoolForm.get('orgSpoc1Email')?.touched"
                            [errorMessage]="'Valid contact person email is required.'">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <app-input-wrapper formControlName="orgSpoc1Phone" id="orgSpoc1Phone"
                            label="Contact Person Phone" type="text" [isRequired]="true"
                            [isErrored]="addNewSchoolForm.get('orgSpoc1Phone')?.invalid && addNewSchoolForm.get('orgSpoc1Phone')?.touched"
                            [errorMessage]="'Valid contact person phone is required.'" [maxLength]="10"
                            [useNumberMask]="true">
                        </app-input-wrapper>
                    </div>

                    <div class="col-lg-12 text-last">
                        <button type="button" class="btn btn-outline-primary katha-btn"
                            (click)="onMetaDataSubmit()">Save</button>
                    </div>
                </div>
            </form>


            <div class="row align-items-center justify-content-between mx-0 px-0 mb-3">
                <div class="col-auto">
                    <span class="heading-text h5" [ngClass]="{'text-muted': !orgId}">Class Details</span>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-outline-primary katha-btn" (click)="createClassRow()"
                        [disabled]="!orgId || hasUnsavedClassRows() || hasUnsavedExistingClassRows()"
                        [ngClass]="{'btn-disabled': !orgId || hasUnsavedClassRows() || hasUnsavedExistingClassRows()}"
                        [title]="getClassButtonTooltip()">
                        Add New Class
                    </button>
                </div>
            </div>

            <!-- Classes List with Pagination -->
            <div [ngClass]="{'disabled-section': !orgId}" [style.opacity]="!orgId ? '0.6' : '1'"
                [style.pointer-events]="!orgId ? 'none' : 'auto'">

                <!-- Classes Table (Unified - Existing and New) -->
                <div class="katha-card mb-3" style="padding: 20px;">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead *ngIf="orgId">
                                <tr>
                                    <th class="p-3">Class Name</th>
                                    <th style="text-align: center;" class="p-3">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Existing Classes (with pagination) -->
                                <ng-container [formGroup]="existingClassesForm">
                                    <ng-container formArrayName="existingClasses">
                                        <tr *ngFor="let dept of ((existingClassesArray?.controls || []) | paginate: { id: 'classes-pagination', itemsPerPage: classLimit, currentPage: classOffset, totalItems: totalClassRecords }); let i = index"
                                            [formGroupName]="getOriginalIndex(dept, existingClassesArray?.controls || [])">
                                            <td class="wrap-text p-2">
                                                <div
                                                    *ngIf="!isExistingClassEditing(getOriginalIndex(dept, existingClassesArray?.controls || []))">
                                                    {{dept.get('deptName')?.value}}
                                                </div>
                                                <div
                                                    *ngIf="isExistingClassEditing(getOriginalIndex(dept, existingClassesArray?.controls || []))">
                                                    <app-input-wrapper formControlName="deptName"
                                                        id="existingDeptName_{{getOriginalIndex(dept, existingClassesArray?.controls || [])}}"
                                                        label="" type="text" [isRequired]="true"
                                                        [isErrored]="dept.get('deptName')?.invalid && dept.get('deptName')?.touched"
                                                        [errorMessage]="'Class name is required.'" [isReadonly]="false">
                                                    </app-input-wrapper>
                                                </div>
                                            </td>
                                            <td style="text-align: center;" class="p-2">
                                                <div class="d-flex justify-content-center align-items-center">
                                                    <button type="button"
                                                        *ngIf="!isExistingClassEditing(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        (click)="toggleExistingClassEdit(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        class="btn btn-sm btn-outline-secondary mr-1"
                                                        style="padding: 4px 8px;">
                                                        <i class="fa fa-edit"></i>
                                                    </button>

                                                    <button
                                                        *ngIf="isExistingClassEditing(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        type="button"
                                                        (click)="saveExistingClass(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        class="btn btn-sm btn-outline-primary mr-1"
                                                        style="padding: 4px 8px;">
                                                        <i class="fa fa-check"></i>
                                                    </button>

                                                    <button
                                                        *ngIf="isExistingClassEditing(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        type="button"
                                                        (click)="cancelExistingClassEdit(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        class="btn btn-sm btn-outline-secondary mr-1"
                                                        style="padding: 4px 8px;">
                                                        <i class="fa fa-times"></i>
                                                    </button>

                                                    <button type="button"
                                                        (click)="deleteExistingClass(getOriginalIndex(dept, existingClassesArray?.controls || []))"
                                                        class="btn btn-sm"
                                                        style="padding: 4px 8px; border: 1px solid rgba(148, 193, 31, 1); color: rgba(148, 193, 31, 1);">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </ng-container>

                                <!-- Add New Class Rows (at the bottom) -->
                                <ng-container [formGroup]="departmentForm">
                                    <ng-container formArrayName="deptNames">
                                        <tr *ngFor="let class of departmentsArray?.controls; let i = index"
                                            [formGroupName]="i">
                                            <td class="wrap-text p-2">
                                                <app-input-wrapper formControlName="deptName" id="deptName_{{i}}"
                                                    label="" type="text" [isRequired]="false"
                                                    [isErrored]="class.get('deptName')?.invalid && class.get('deptName')?.touched"
                                                    [errorMessage]="'Class name is required.'"
                                                    placeholder="Enter class name">
                                                </app-input-wrapper>
                                            </td>
                                            <td style="text-align: center;" class="p-2">
                                                <div class="d-flex justify-content-center align-items-center">
                                                    <button type="button" (click)="saveClass(i)"
                                                        class="btn btn-sm btn-outline-primary mr-1"
                                                        style="padding: 4px 8px;">
                                                        <i class="fa fa-check"></i>
                                                    </button>

                                                    <button type="button" (click)="cancelNewClass(i)"
                                                        class="btn btn-sm btn-outline-secondary mr-1"
                                                        style="padding: 4px 8px;">
                                                        <i class="fa fa-times"></i>
                                                    </button>

                                                    <button *ngIf="departmentsArray.length > 1" type="button"
                                                        (click)="removeRow(i, 'departmentForm')" class="btn btn-sm"
                                                        style="padding: 4px 8px; border: 1px solid rgba(148, 193, 31, 1); color: rgba(148, 193, 31, 1);">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </ng-container>

                                <!-- Empty state row when no classes exist -->
                                <tr *ngIf="departmentsData.length === 0 && departmentsArray.length === 0">
                                    <td colspan="2" class="p-2" style="text-align: center;">
                                        <div *ngIf="!orgId" class="alert alert-info"
                                            style="margin: 0; background-color: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d;">
                                            <i class="fa fa-info-circle me-2"></i>
                                            Please save the school information first before adding classes.
                                        </div>
                                        <div *ngIf="orgId" class="mt-1" style="font-size: 11px; color: #6c757d;">
                                            <i class="fa fa-info-circle me-1"></i>
                                            No classes available. Click "Add New Class" to create your first class.
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls for Classes -->
                    <div class="form-group row pagination" *ngIf="departmentsData && departmentsData.length > 0">
                        <div class="col-md-4 d-flex align-items-center">
                            <div class="dropdown-wrapper position-relative">
                                <app-single-select-dropdown [options]="[
                                  { label: '10', value: 10 },
                                  { label: '20', value: 20 },
                                  { label: '30', value: 30 },
                                  { label: '40', value: 40 }
                                ]" [labelKey]="'label'" [valueKey]="'value'"
                                    [readonly]="departmentsData && departmentsData.length < 10" [(ngModel)]="classLimit"
                                    (selectionChange)="onClassPageSizeChanged($event)">
                                </app-single-select-dropdown>
                            </div>
                        </div>

                        <div class="col-md-8 pagination d-flex justify-content-end" style="padding-top: 14px;">
                            <pagination-controls class="custom-pagination" id="classes-pagination"
                                (pageChange)="onClassCurrentPageChanged($event)" previousLabel="&larr;"
                                nextLabel="&rarr;">
                            </pagination-controls>
                        </div>
                    </div>
                </div>


            </div>
            <div class="row align-items-center justify-content-between mx-0 px-0 mb-3">
                <div class="col-auto">
                    <span class="heading-text h5"
                        [ngClass]="{'text-muted': !orgId || departmentsData.length === 0}">User Details</span>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-outline-primary katha-btn" (click)="createUserRow()"
                        [disabled]="!orgId || departmentsData.length === 0 || hasUnsavedUserRows() || hasUnsavedExistingUserRows()"
                        [ngClass]="{'btn-disabled': !orgId || departmentsData.length === 0 || hasUnsavedUserRows() || hasUnsavedExistingUserRows()}"
                        [title]="getUserButtonTooltip()">
                        Add New User
                    </button>
                </div>
            </div>
            <!-- Users List with Pagination -->
            <div [ngClass]="{'disabled-section': !orgId || departmentsData.length === 0}"
                [style.opacity]="!orgId || departmentsData.length === 0 ? '0.6' : '1'"
                [style.pointer-events]="!orgId || departmentsData.length === 0 ? 'none' : 'auto'">

                <!-- Users Table (Unified - Existing and New) -->
                <div class="katha-card mb-3" style="padding: 20px;"
                    *ngIf="usersData.length > 0 || orgUserArray.length > 0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead *ngIf="usersData.length > 0 || orgUserArray.length > 0">
                                <tr>
                                    <th class="p-3">User Name</th>
                                    <th class="p-3">Email</th>
                                    <th class="p-3">Phone Number</th>
                                    <th style="text-align: center;" class="p-3">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Existing Users (with pagination) -->
                                <ng-container [formGroup]="existingUsersForm">
                                    <ng-container formArrayName="existingUsers">
                                        <ng-container
                                            *ngFor="let user of ((existingUsersArray?.controls || []) | paginate: { id: 'users-pagination', itemsPerPage: userLimit, currentPage: userOffset, totalItems: totalUserRecords }); let i = index">
                                            <!-- Main User Row -->
                                            <tr
                                                [formGroupName]="getOriginalIndex(user, existingUsersArray?.controls || [])">
                                                <td class="wrap-text p-2">
                                                    <div
                                                        *ngIf="!isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))">
                                                        {{user.get('userName')?.value}}
                                                    </div>
                                                    <div *ngIf="isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                        style="height: 80px; display: flex; flex-direction: column;">
                                                        <div style="flex: 0 0 auto;">
                                                            <app-input-wrapper formControlName="userName"
                                                                id="userName_{{getOriginalIndex(user, existingUsersArray?.controls || [])}}"
                                                                label="" type="text" [isRequired]="false"
                                                                [isErrored]="false" [errorMessage]="''"
                                                                placeholder="Enter user name">
                                                            </app-input-wrapper>
                                                        </div>
                                                        <div
                                                            style="flex: 1 1 auto; min-height: 30px; display: flex; align-items: flex-start; padding-bottom: 5px;">
                                                            <div *ngIf="user.get('userName')?.invalid && user.get('userName')?.touched"
                                                                class="error-msg"
                                                                style="font-size: 11px; color: #dc3545; margin-top: 2px; line-height: 1.3;">
                                                                User name is required.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="wrap-text p-2">
                                                    <div
                                                        *ngIf="!isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))">
                                                        {{user.get('userEmailId')?.value}}
                                                    </div>
                                                    <div *ngIf="isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                        style="height: 80px; display: flex; flex-direction: column;">
                                                        <div style="flex: 0 0 auto;">
                                                            <app-input-wrapper formControlName="userEmailId"
                                                                id="userEmailId_{{getOriginalIndex(user, existingUsersArray?.controls || [])}}"
                                                                label="" type="email" [isRequired]="false"
                                                                [isErrored]="false" [errorMessage]="''"
                                                                placeholder="Enter email">
                                                            </app-input-wrapper>
                                                        </div>
                                                        <div
                                                            style="flex: 1 1 auto; min-height: 30px; display: flex; align-items: flex-start; padding-bottom: 5px;">
                                                            <div *ngIf="user.get('userEmailId')?.invalid && user.get('userEmailId')?.touched"
                                                                class="error-msg"
                                                                style="font-size: 11px; color: #dc3545; margin-top: 2px; line-height: 1.3;">
                                                                Valid email is required.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="wrap-text p-2">
                                                    <div
                                                        *ngIf="!isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))">
                                                        {{user.get('userPhoneNumber')?.value}}
                                                    </div>
                                                    <div *ngIf="isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                        style="height: 80px; display: flex; flex-direction: column;">
                                                        <div style="flex: 0 0 auto;">
                                                            <app-input-wrapper formControlName="userPhoneNumber"
                                                                id="userPhoneNumber_{{getOriginalIndex(user, existingUsersArray?.controls || [])}}"
                                                                label="" type="text" [isRequired]="false"
                                                                [maxLength]="10" [isErrored]="false" [errorMessage]="''"
                                                                placeholder="Enter phone number" [useNumberMask]="true">
                                                            </app-input-wrapper>
                                                        </div>
                                                        <div
                                                            style="flex: 1 1 auto; min-height: 30px; display: flex; align-items: flex-start; padding-bottom: 5px;">
                                                            <div *ngIf="user.get('userPhoneNumber')?.invalid && user.get('userPhoneNumber')?.touched"
                                                                class="error-msg"
                                                                style="font-size: 11px; color: #dc3545; margin-top: 2px; line-height: 1.3;">
                                                                Valid phone number is required.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td style="text-align: center;" class="p-2">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <button
                                                            *ngIf="!isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            type="button"
                                                            (click)="toggleExistingUserEdit(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            class="btn btn-sm btn-outline-secondary mr-1"
                                                            style="padding: 4px 8px;">
                                                            <i class="fa fa-edit"></i>
                                                        </button>

                                                        <button
                                                            *ngIf="isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            type="button"
                                                            (click)="saveExistingUser(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            class="btn btn-sm btn-outline-primary mr-1"
                                                            style="padding: 4px 8px;">
                                                            <i class="fa fa-check"></i>
                                                        </button>

                                                        <button
                                                            *ngIf="isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            type="button"
                                                            (click)="cancelExistingUserEdit(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            class="btn btn-sm btn-outline-secondary mr-1"
                                                            style="padding: 4px 8px;">
                                                            <i class="fa fa-times"></i>
                                                        </button>

                                                        <button type="button"
                                                            (click)="deleteExistingUser(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                            class="btn btn-sm"
                                                            style="padding: 4px 8px; border: 1px solid rgba(148, 193, 31, 1); color: rgba(148, 193, 31, 1);">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <!-- Class Display Sub-row for Existing Users (Read-only) -->
                                            <tr *ngIf="!isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || [])) && user.get('deptName')?.value"
                                                [formGroupName]="getOriginalIndex(user, existingUsersArray?.controls || [])"
                                                class="class-sub-row">
                                                <td colspan="3" class="class-sub-row-cell">
                                                    <div class="class-display-container">
                                                        <span class="class-label">Classes:</span>
                                                        <span class="class-values">
                                                            <span *ngIf="isArray(user.get('deptName')?.value)">
                                                                {{user.get('deptName')?.value.join(', ')}}
                                                            </span>
                                                            <span *ngIf="!isArray(user.get('deptName')?.value)">
                                                                {{user.get('deptName')?.value}}
                                                            </span>
                                                        </span>
                                                    </div>
                                                </td>
                                            </tr>

                                            <!-- Class Selection Sub-row for Existing Users (Edit mode) -->
                                            <tr *ngIf="isExistingUserEditing(getOriginalIndex(user, existingUsersArray?.controls || []))"
                                                [formGroupName]="getOriginalIndex(user, existingUsersArray?.controls || [])"
                                                class="class-sub-row">
                                                <td colspan="3" class="class-sub-row-cell">
                                                    <div class="class-selection-container">
                                                        <div class="class-label">Select Classes:</div>
                                                        <div class="class-checkbox-grid"
                                                            *ngIf="departmentsData.length > 0"
                                                            style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; width: 100%;">
                                                            <div class="class-checkbox-item"
                                                                *ngFor="let dept of departmentsData">
                                                                <label class="form-check">
                                                                    <input class="form-check-input" type="checkbox"
                                                                        [id]="'existingUser_' + getOriginalIndex(user, existingUsersArray?.controls || []) + '_dept_' + dept.deptId"
                                                                        [checked]="isClassSelectedForExistingUser(getOriginalIndex(user, existingUsersArray?.controls || []), dept.deptName)"
                                                                        (change)="onExistingUserClassChange(getOriginalIndex(user, existingUsersArray?.controls || []), dept.deptName, $event)">
                                                                    <span class="input-helper"></span>
                                                                    <span
                                                                        class="form-check-label">{{dept.deptName}}</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div *ngIf="departmentsData.length === 0"
                                                            class="no-classes-message">
                                                            {{isLoadingDepartments ? 'Loading classes...' : 'No classes
                                                            available'}}
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>

                                <!-- Add New User Rows (at the bottom) -->
                                <ng-container [formGroup]="orgUserForm">
                                    <ng-container formArrayName="user">
                                        <ng-container *ngFor="let user of orgUserArray?.controls; let i = index">
                                            <!-- Main User Row -->
                                            <tr [formGroupName]="i">
                                                <td class="wrap-text p-2">
                                                    <div style="height: 80px; display: flex; flex-direction: column;">
                                                        <div style="flex: 0 0 auto;">
                                                            <app-input-wrapper formControlName="name" id="name_{{i}}"
                                                                label="" type="text" [isRequired]="false"
                                                                [isErrored]="false" [errorMessage]="''"
                                                                placeholder="Enter user name">
                                                            </app-input-wrapper>
                                                        </div>
                                                        <div
                                                            style="flex: 1 1 auto; min-height: 30px; display: flex; align-items: flex-start; padding-bottom: 5px;">
                                                            <div *ngIf="user.get('name')?.invalid && user.get('name')?.touched"
                                                                class="error-msg"
                                                                style="font-size: 11px; color: #dc3545; margin-top: 2px; line-height: 1.3;">
                                                                User name is required.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="wrap-text p-2">
                                                    <div style="height: 80px; display: flex; flex-direction: column;">
                                                        <div style="flex: 0 0 auto;">
                                                            <app-input-wrapper formControlName="userEmailId"
                                                                id="userEmailId_{{i}}" label="" type="email"
                                                                [isRequired]="false" [isErrored]="false"
                                                                [errorMessage]="''" placeholder="Enter email">
                                                            </app-input-wrapper>
                                                        </div>
                                                        <div
                                                            style="flex: 1 1 auto; min-height: 30px; display: flex; align-items: flex-start; padding-bottom: 5px;">
                                                            <div *ngIf="user.get('userEmailId')?.invalid && user.get('userEmailId')?.touched"
                                                                class="error-msg"
                                                                style="font-size: 11px; color: #dc3545; margin-top: 2px; line-height: 1.3;">
                                                                Valid email is required.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="wrap-text p-2">
                                                    <div style="height: 80px; display: flex; flex-direction: column;">
                                                        <div style="flex: 0 0 auto;">
                                                            <app-input-wrapper formControlName="userPhoneNumber"
                                                                id="userPhoneNumber_{{i}}" label="" type="text"
                                                                [isRequired]="false" [maxLength]="10"
                                                                [isErrored]="false" [errorMessage]="''"
                                                                placeholder="Enter phone number" [useNumberMask]="true">
                                                            </app-input-wrapper>
                                                        </div>
                                                        <div
                                                            style="flex: 1 1 auto; min-height: 30px; display: flex; align-items: flex-start; padding-bottom: 5px;">
                                                            <div *ngIf="user.get('userPhoneNumber')?.invalid && user.get('userPhoneNumber')?.touched"
                                                                class="error-msg"
                                                                style="font-size: 11px; color: #dc3545; margin-top: 2px; line-height: 1.3;">
                                                                Valid phone number is required.
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td style="text-align: center;" class="p-2">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <button type="button" (click)="saveUser(i)"
                                                            class="btn btn-sm btn-outline-primary mr-1"
                                                            style="padding: 4px 8px;">
                                                            <i class="fa fa-check"></i>
                                                        </button>

                                                        <button type="button" (click)="cancelNewUser(i)"
                                                            class="btn btn-sm btn-outline-secondary mr-1"
                                                            style="padding: 4px 8px;">
                                                            <i class="fa fa-times"></i>
                                                        </button>

                                                        <button *ngIf="orgUserArray.length > 1" type="button"
                                                            (click)="removeRow(i, 'orgUserForm')" class="btn btn-sm"
                                                            style="padding: 4px 8px; border: 1px solid rgba(148, 193, 31, 1); color: rgba(148, 193, 31, 1);">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <!-- Class Selection Sub-row for New Users -->
                                            <tr [formGroupName]="i" class="class-sub-row">
                                                <td colspan="3" class="class-sub-row-cell">
                                                    <div class="class-selection-container">
                                                        <div class="class-label">Select Classes:</div>
                                                        <div class="class-checkbox-grid"
                                                            *ngIf="departmentsData.length > 0"
                                                            style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; width: 100%;">
                                                            <div class="class-checkbox-item"
                                                                *ngFor="let dept of departmentsData">
                                                                <label class="form-check">
                                                                    <input class="form-check-input" type="checkbox"
                                                                        [id]="'newUser_' + i + '_dept_' + dept.deptId"
                                                                        [checked]="isClassSelectedForNewUser(i, dept.deptId)"
                                                                        (change)="onNewUserClassChange(i, dept.deptId, $event)">
                                                                    <span class="input-helper"></span>
                                                                    <span
                                                                        class="form-check-label">{{dept.deptName}}</span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div *ngIf="departmentsData.length === 0"
                                                            class="no-classes-message">
                                                            {{isLoadingDepartments ? 'Loading classes...' : (!orgId ?
                                                            'Save school information first' : 'No classes available')}}
                                                        </div>
                                                        <div *ngIf="user.get('orguDepartmentIds')?.invalid && user.get('orguDepartmentIds')?.touched"
                                                            class="error-msg">
                                                            Class selection is required.
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </ng-container>
                                </ng-container>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls for Users -->
                    <div class="form-group row pagination" *ngIf="usersData && usersData.length > 0">
                        <div class="col-md-4 d-flex align-items-center">
                            <div class="dropdown-wrapper position-relative">
                                <app-single-select-dropdown [options]="[
                                  { label: '10', value: 10 },
                                  { label: '20', value: 20 },
                                  { label: '30', value: 30 },
                                  { label: '40', value: 40 }
                                ]" [labelKey]="'label'" [valueKey]="'value'"
                                    [readonly]="usersData && usersData.length < 10" [(ngModel)]="userLimit"
                                    (selectionChange)="onUserPageSizeChanged($event)">
                                </app-single-select-dropdown>
                            </div>
                        </div>

                        <div class="col-md-8 pagination d-flex justify-content-end" style="padding-top: 14px;">
                            <pagination-controls class="custom-pagination" id="users-pagination"
                                (pageChange)="onUserCurrentPageChanged($event)" previousLabel="&larr;"
                                nextLabel="&rarr;">
                            </pagination-controls>
                        </div>
                    </div>
                </div>


                <!-- Empty state when no users exist -->
                <div class="katha-card mb-3" style="padding: 20px;"
                    *ngIf="usersData.length === 0 && orgUserArray.length === 0">
                    <div class="text-center p-3">
                        <div *ngIf="!orgId || departmentsData.length === 0" class="alert alert-info"
                            style="margin: 0; background-color: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d;">
                            <i class="fa fa-info-circle me-2"></i>
                            <span *ngIf="!orgId">Please save the school information first before adding users.</span>
                            <span *ngIf="orgId && departmentsData.length === 0">Please add classes in the "Class
                                Details" section above before adding users.</span>
                        </div>
                        <div *ngIf="orgId && departmentsData.length > 0" class="mt-1"
                            style="font-size: 11px; color: #6c757d;">
                            <i class="fa fa-info-circle me-1"></i>
                            No users available. Click "Add New User" to create your first user.
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-start mt-5">
                <button type="button" class="btn btn-outline-secondary katha-btn mr-3"
                    routerLink="/school-list">Back</button>
            </div>

        </div>

    </div>
</app-sidebar>

<!-- Delete Class Confirmation Modal -->
<div class="modal" id="deleteClassModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content katha-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">
                    Are you sure you want to delete the class "{{classToDelete?.name}}"?
                </h5>
                <button type="button" (click)="hideModal('deleteClassModal')" class="close"
                    data-bs-dismiss="modal">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-outline-secondary"
                    (click)="hideModal('deleteClassModal')">Cancel</button>
                <button (click)="confirmDeleteClass()" type="submit" class="btn btn-outline-primary">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Confirmation Modal -->
<div class="modal" id="deleteUserModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content katha-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">
                    Are you sure you want to delete the user "{{userToDelete?.name}}"?
                </h5>
                <button type="button" (click)="hideModal('deleteUserModal')" class="close"
                    data-bs-dismiss="modal">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-outline-secondary"
                    (click)="hideModal('deleteUserModal')">Cancel</button>
                <button (click)="confirmDeleteUser()" type="submit" class="btn btn-outline-primary">Delete</button>
            </div>
        </div>
    </div>
</div>