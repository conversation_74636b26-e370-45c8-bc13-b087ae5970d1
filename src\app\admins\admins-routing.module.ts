import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '../Guards/auth.guard';
import { AddNewPlaylistComponent } from './playlists management/add-new-playlist/add-new-playlist.component';
import { PlaylistsComponent } from './playlists management/playlists/playlists.component';
import { AddEditSchoolComponent } from './school managment/add-edit-school/add-edit-school.component';
import { SchoolsComponent } from './school managment/schools/schools.component';
import { AddStoryComponent } from './story management/add-story/add-story.component';
import { RecommendationComponent } from './story management/recommendation/recommendation.component';
import { RequestsComponent } from './story management/requests/requests.component';
import { StoriesComponent } from './story management/stories/stories.component';
import { PlanListComponent } from './subscription plans managment/plan-list/plan-list.component';
import { AppUsersComponent } from './users/app-users/app-users.component';
import { PortalUsersComponent } from './users/portal-users/portal-users.component';

const routes: Routes = [
  { path: 'plan-list', component: PlanListComponent, canActivate: [AuthGuard] },

  {
    path: 'stories',
    canActivate: [AuthGuard],
    children: [
      { path: '', component: StoriesComponent },
      { path: 'story', component: AddStoryComponent },
      { path: 'recommendations', component: RecommendationComponent },
      { path: 'requests', component: RequestsComponent },
    ],
  },

  { path: 'app-users', component: AppUsersComponent, canActivate: [AuthGuard] },

  { path: 'portal-users', component: PortalUsersComponent, canActivate: [AuthGuard] },

  {
    path: 'school-list',
    canActivate: [AuthGuard],
    children: [
      { path: '', component: SchoolsComponent },
      { path: 'school', component: AddEditSchoolComponent },
      { path: 'school/:orgId', component: AddEditSchoolComponent },
    ],
  },


  {
    path: 'playlists',
    canActivate: [AuthGuard],
    children: [
      { path: '', component: PlaylistsComponent },
      { path: 'playlist', component: AddNewPlaylistComponent },
      { path: 'playlist/:schoolId', component: AddNewPlaylistComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminsRoutingModule { }
