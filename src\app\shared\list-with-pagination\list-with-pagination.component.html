<div class="row" *ngIf="data&&data.length > 0">
    <div class="col-md-12">
        <div class="card katha-card">
            <div *ngIf="cardTitle" class="card-title tablePaddingx heading-text mb-1 py-3">{{cardTitle}}</div>
            <div class="card-body px-0 py-0">
                <div class="table-responsive">
                    <table class="table table-sm mb-0">
                        <thead>
                            <tr>
                                <th class="py-3"
                                    [ngClass]="{'text-last':column.title ==='Regular Price'|| column.title ==='Offer Price' || column.title==='Unlock/Lock',
                                            'text-center': column.dataKey === 'sfLanguageName' || column.dataKey === 'ageGroup'|| column.dataKey === 'stTags' || column.dataKey === 'managePlaylist' || column.dataKey === 'editSchoolInfo'}"
                                    *ngFor="let column of columns">{{column.title}}</th>
                                <th style="text-align: end;" class="p-3" *ngIf="actionPermissions">Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let element of data | paginate: { itemsPerPage:  perPageItems, currentPage: p, totalItems: totalNumberOfRecords };
                                 let indexOfelement = index;" (click)="onRowClick(element)"
                                [ngClass]="{'cursor-pointer':this.module==='REQUESTS'}">
                                <ng-container *ngFor="let column of columns; let columIndex = index;">
                                    <td [ngClass]="{'text-last':column.title === 'Duration'|| column.title ==='Regular Price'|| column.title ==='Offer Price'}"
                                        *ngIf="column.dataKey !== 'isActive' && column.dataKey !== 'sfLanguageName' && column.dataKey !== 'stTags' && column.dataKey !== 'userStatus' && column.dataKey !== 'ageGroup' && column.dataKey !== 'schoolStatus' && column.dataKey !== 'managePlaylist' && column.dataKey !== 'editSchoolInfo'"
                                        class="wrap-text p-2">
                                        {{ element[column.dataKey] }}
                                    </td>

                                    <!-- Manage Playlist Column -->
                                    <td class="text-center p-2" *ngIf="column.dataKey === 'managePlaylist'">
                                        <a class="btn my-0 py-0" (click)="onManagePlaylistClick(element)"
                                            ngbTooltip="Manage Playlist" style="cursor: pointer;">
                                            <img style="border-radius: 0% !important; height: 18px !important; width: 18px !important; pointer-events: none;"
                                                src="./assets/icons/Playlist.svg" alt="Playlists">
                                        </a>
                                    </td>

                                    <!-- Edit School Info Column -->
                                    <td class="text-center p-2" *ngIf="column.dataKey === 'editSchoolInfo'">
                                        <a class="btn my-0 py-0" ngbTooltip="Edit School Info">
                                            <i class="fa fa-pencil" (click)="onEditClick(modalId,element)"
                                                placement="top"></i>
                                        </a>
                                    </td>

                                    <td class="text-center"
                                        *ngIf="column.dataKey === 'sfLanguageName'||column.dataKey === 'ageGroup'">
                                        <ng-container
                                            *ngIf="element[column.dataKey]?.length <= 1; else multipleLanguages">
                                            {{
                                            element[column.dataKey]?element[column.dataKey][0]?element[column.dataKey][0]:'-':'-'
                                            }}
                                        </ng-container>
                                        <ng-template #multipleLanguages>
                                            <a (click)="showCommonModal('commonModal',column.dataKey,element[column.dataKey]);$event.stopPropagation()"
                                                class="btn my-0 py-0 icon-red">
                                                <i class="fa fa-eye" ngbTooltip="View Languages"></i>
                                            </a>
                                        </ng-template>
                                    </td>

                                    <td class="text-center" *ngIf="column.dataKey === 'stTags'">
                                        <a (click)="showCommonModal('commonModal','tags',element);$event.stopPropagation()"
                                            class="btn my-0 py-0 icon-red">
                                            <i class="fa fa-eye" ngbTooltip="View Tags"></i>
                                        </a>
                                    </td>

                                    <td [ngClass]="[element[column.dataKey] ?'color-secondary':'text-danger']"
                                        *ngIf="column.dataKey == 'isActive'" class="wrap-text p-2">
                                        {{ element[column.dataKey] ? 'Active' : 'Inactive' }}
                                    </td>
                                    <!-- <td *ngIf="column.dataKey ==='userProfilePicture'">
                                        <img
                                            [src]="element[column.dataKey] ? element[column.dataKey] : defProfilePicture">
                                    </td> -->

                                    <td class="wrap-text d-flex justify-content-start py-2 px-0 m-0"
                                        *ngIf="column.dataKey === 'userStatus'||column.dataKey === 'schoolStatus'">
                                        <div class="custom-control d-flex justify-content-center custom-switch p-0 m-0">
                                            <!-- <input type="checkbox" class="custom-control-input"
                                                id="switch{{indexOfelement}}" [checked]="element.userStatus=='LOCKED'"
                                                (click)="onSwitchClick($event, element)" />
                                            <label class="custom-control-label" for="switch{{indexOfelement}}"></label> -->

                                            <label [ngClass]="{'inner-shadow': verifyIsChecked(element)}"
                                                class="checkbox-container" (click)="onSwitchClick($event, element)">

                                                <input type="checkbox" hidden [checked]="verifyIsChecked(element)"
                                                    (change)="onCheckboxChange($event)" />

                                                <div class="toggle-container">
                                                    <span class="checkmark"
                                                        [ngClass]="{'checked': verifyIsChecked(element)}"></span>
                                                    <span class="checkbox-label">{{ verifyIsChecked(element) ?
                                                        activeLabel : inactiveLabel }}</span>
                                                </div>

                                            </label>

                                        </div>
                                    </td>



                                    <!-- <td style="min-width: 120px;" class="wrap-text p-2"
                                        *ngIf="column.title == 'Ratings'">

                                        <i *ngFor="let starType of getStarsArray(showStaticPagination? element.bookRatings:element.ratings)" class="fa" [ngClass]="[
                                        starType === 'full' ? 'fas fa-star filled-star' : '',
                                        starType === 'half' ? 'fas fa-star-half-alt half-star' : '',
                                        starType === 'empty' ? 'far fa-star empty-star' : ''
                                         ]">
                                        </i>
                                    </td> -->

                                    <!-- <td class="wrap-text p-2 text-last" [ngClass]="{'text-start':column.title === 'Requests'}"
                                        *ngIf="column.title == 'Review'||column.title == 'Discussion questions'||column.title == 'Review'||column.title == 'Requests'">
                                        <a href="javascript:void(0);" class="review-link"
                                            (click)="setSelectedElement(element,column.title);">Read</a>
                                    </td> -->
                                </ng-container>
                                <td style="text-align: end;" class="p-2" *ngIf="actionPermissions">
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.members" class="btn my-0 py-0">
                                        <i class="fa fa-user-group" placement="top" ngbTooltip="Members"></i>
                                    </a>

                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.playlist" ngbTooltip="Playlist" class="btn my-0 py-0"
                                        routerLink="/admins/school-list/select-class">
                                        <img style="border-radius: 0% !important; height: 18px !important; width: 18px !important; pointer-events: none;"
                                            src="./assets/icons/Playlist.svg" alt="Playlists">
                                    </a>

                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.add&&element.isAddBtn" class="btn my-0 py-0">
                                        <i class="fa fa-plus" placement="top"
                                            (click)="triggerOpenModal(modalId,false,element)" ngbTooltip="Add"></i>
                                    </a>

                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.remove" class="btn my-0 py-0">
                                        <i class="fa fa-xmark" placement="top"
                                            (click)="triggerOpenModal(modalId,true,element)" ngbTooltip="Remove"></i>
                                    </a>

                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.view" class="btn my-0 py-0">
                                        <i class="fa fa-eye" placement="top" ngbTooltip="View"></i>
                                    </a>
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.edit" class="btn my-0 py-0">
                                        <i class="fa fa-pencil" (click)="onEditClick(modalId,element)" placement="top"
                                            ngbTooltip="Edit"></i>
                                    </a>
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.delete" class="btn my-0 py-0">
                                        <i class="fa fa-trash text-danger"
                                            (click)="showModal('deleteRecordModal',element[idKey])" placement="top"
                                            ngbTooltip="Delete"></i>
                                    </a>

                                </td>
                            </tr>
                        </tbody>

                    </table>
                </div>

            </div>
        </div>
    </div>
</div>
<div class="form-group row pagination" *ngIf="data&&data.length>0&&showPagination">
    <div class="col-md-4 d-flex align-items-center">
        <div class="dropdown-wrapper  position-relative">
            <!-- <select [disabled]="data && data.length < 10" [ngClass]="{'disable-btn': data && data.length < 9}"
                name="perPageItems" id="perPageItems" class="form-control katha-card page-numbers" [(ngModel)]="perPageItems"
                (ngModelChange)="handlePageSizeChange($event)">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="30">30</option>
                <option value="40">40</option>
            </select> -->
            <app-single-select-dropdown [options]="[
              { label: '10', value: 10 },
              { label: '20', value: 20 },
              { label: '30', value: 30 },
              { label: '40', value: 40 }
            ]" [labelKey]="'label'" [valueKey]="'value'" [readonly]="data && data.length < 10"
                [(ngModel)]="perPageItems" (selectionChange)="handlePageSizeChange($event)">
            </app-single-select-dropdown>
            <!-- <i class="fa-solid fa-chevron-down downArrow"></i> -->
        </div>
    </div>

    <div class="col-md-8 pagination" style="padding-top: 14px;">
        <pagination-controls class="custom-pagination" (pageChange)="pageChange($event)" previousLabel="&larr;"
            nextLabel="&rarr;">
        </pagination-controls>
    </div>

</div>
<div class="row" *ngIf="data?data.length==0:!data">
    <div class="col-md-12">
        <div class="card katha-card">
            <div class="card-body">
                No data available
            </div>
        </div>
    </div>
</div>





<!-- Delete Warning Modal -->
<div class="modal" id="deleteRecordModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content katha-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">Are you sure you want to delete this
                    {{modalTitle|lowercase}}?
                </h5>
                <button type="button" (click)="hideModal('deleteRecordModal')" class="close"
                    data-bs-dismiss="modal">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn  btn-outline-secondary"
                    (click)="hideModal('deleteRecordModal')">Cancel</button>
                <button type="submit" (click)="deleteRecord()" class="btn btn-outline-primary">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- common modal -->
<div class="modal" id="commonModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content katha-modal">
            <div class="modal-header d-flex justify-content-end" style="border-bottom: none;">
                <button type="button" (click)="hideModal('commonModal')" class="close"
                    data-bs-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body my-0 py-0">
                <p class="commonModalContent">{{ commonModalHeader }}</p>
                <p class="commonModalContent">{{ commonModalData }}</p>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn w-100 btn-outline-primary"
                    (click)="hideModal('commonModal')">Ok</button>
            </div>
        </div>
    </div>
</div>



<div class="modal" id="lockUnlockModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content katha-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">
                    Are you sure you want to {{switchBtnData?.userStatus=='LOCKED'?'unlock':'lock'}} this user?
                </h5>
                <button type="button" (click)="hideModal('lockUnlockModal')" class="close"
                    data-bs-dismiss="modal">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-outline-secondary"
                    (click)="hideModal('lockUnlockModal')">Cancel</button>
                <button (click)="onSwitchModalClick()" type="submit" class="btn btn-outline-primary">
                    {{ switchBtnData?.userStatus=='LOCKED'?'Unlock':'Lock' }}
                </button>
            </div>
        </div>
    </div>
</div>