import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import {
  getBreadCrumbModules
} from 'src/app/config/commonHelper';
import { ModuleTypes, OrgTypes } from 'src/app/config/constants';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { OrgnizationService } from 'src/app/shared/services/orgnization.service';
@Component({
  selector: 'app-add-edit-school',
  templateUrl: './add-edit-school.component.html',
  styleUrls: ['./add-edit-school.component.scss'],
})
export class AddEditSchoolComponent implements OnInit, OnDestroy {
  _tomodule: string = ModuleTypes.ADDEDITSCHOOL;
  addNewSchoolForm: FormGroup;
  orgUserForm: FormGroup;
  departmentForm: FormGroup;
  existingClassesForm: FormGroup;
  menuTitle: string = 'Add School';
  isEdit: boolean = false;
  params: any;
  selectedFile: File | null = null;
  isLargeScreen: boolean = true;
  classData = [
    { id: 1, name: 'Grade 1' },
    { id: 2, name: 'Grade 2' },
    { id: 3, name: 'Grade 3' },
    { id: 4, name: 'Grade 4' },
  ];
  departmentsData: any[] = [];
  isLoadingDepartments: boolean = false;
  breadCrumbModules: any;
  orgId: string | number;
  private readonly SCHOOL_DATA_KEY = 'editSchoolData';
  private readonly SCHOOL_ORG_ID_KEY = 'editSchoolOrgId';

  // User management properties
  usersData: any[] = [];
  isLoadingUsers: boolean = false;
  existingUsersForm: FormGroup;

  // Role management properties
  rolesData: any[] = [];
  isLoadingRoles: boolean = false;
  userRoleId: number | null = null;

  // Pagination properties for classes
  classLimit: number = 10; // Use default value instead of Constants.limit
  classOffset: number = 1; // Use default value instead of Constants.offset
  totalClassRecords: number = 0;
  classColumns: any[] = [
    { title: 'Class Name', dataKey: 'deptName' }
  ];

  // Pagination properties for users
  userLimit: number = 10; // Use default value instead of Constants.limit
  userOffset: number = 1; // Use default value instead of Constants.offset
  totalUserRecords: number = 0;
  userColumns: any[] = [
    { title: 'User Name', dataKey: 'userName' },
    { title: 'Email', dataKey: 'userEmailId' },
    { title: 'Phone Number', dataKey: 'userPhoneNumber' },
    { title: 'Classes', dataKey: 'deptName' }
  ];

  constructor(
    private toastr: ToastrService,
    private readonly ngxSpinnerService: NgxSpinnerService,
    private readonly formBuilder: FormBuilder,
    private readonly orgnaizationService: OrgnizationService,
    private readonly dataTransferService: DataTransferService,
    private readonly route: ActivatedRoute,
  ) {
    this.addNewSchoolForm = this.formBuilder.group({
      orgTitle: ['', Validators.required],
      orgEmail: ['', [Validators.required, this.emailValidator]],
      orgPhone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      orgSpoc1FullName: ['', Validators.required],
      orgSpoc1Email: ['', [Validators.required, this.emailValidator]],
      orgSpoc1Phone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      orgAddress: ['', Validators.required],
      orgType: [OrgTypes.SCHOOL],
    });

    this.orgUserForm = this.formBuilder.group({
      user: this.formBuilder.array([]),
    });

    this.departmentForm = this.formBuilder.group({
      deptNames: this.formBuilder.array([]),
    });

    this.existingClassesForm = this.formBuilder.group({
      existingClasses: this.formBuilder.array([]),
    });

    this.existingUsersForm = this.formBuilder.group({
      existingUsers: this.formBuilder.array([]),
    });
  }

  // Custom email validator that requires proper domain extension
  emailValidator(control: any): { [key: string]: any } | null {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }

    // Enhanced email regex that requires proper domain extension
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (!emailRegex.test(control.value)) {
      return { 'invalidEmail': true };
    }

    return null;
  }

  ngOnInit(): void {
    this.checkScreenSize();
    this.loadRoles(); // Load roles first as they're needed for user creation
    this.initializeSchoolData();
    // loadDepartments will be called after orgId is available
    this.breadCrumbModules = getBreadCrumbModules(ModuleTypes.ADDEDITSCHOOL, { isEdit: this.isEdit });
  }

  ngOnDestroy(): void {
    // Clean up localStorage when component is destroyed
    localStorage.removeItem(this.SCHOOL_DATA_KEY);
    localStorage.removeItem(this.SCHOOL_ORG_ID_KEY);
  }

  private initializeSchoolData(): void {
    // Get navigation state
    this.params = history?.state;

    // Try to get orgId from multiple sources in order of preference:
    // 1. Route parameters (survives page refresh)
    // 2. Navigation state (lost on refresh)
    // 3. localStorage (backup for refresh)

    const routeOrgId = this.route.snapshot.paramMap.get('orgId');
    const stateOrgId = this.extractOrgIdFromState();
    const cachedOrgId = localStorage.getItem(this.SCHOOL_ORG_ID_KEY);

    this.orgId = routeOrgId || stateOrgId || cachedOrgId || '';

    // Determine if we're in edit mode
    this.isEdit = !!(this.params?.isEdit || this.orgId);



    if (this.isEdit && this.orgId) {
      // Store orgId in localStorage for page refresh scenarios
      localStorage.setItem(this.SCHOOL_ORG_ID_KEY, this.orgId.toString());
      this.loadSchoolData();
    }

    // Load departments and users if orgId is available (for both edit and add modes)
    if (this.orgId) {
      this.loadDepartments();
      this.loadUsers();
    } else {
      // No orgId available, set empty state
      this.departmentsData = [];
      this.isLoadingDepartments = false;
      this.usersData = [];
      this.isLoadingUsers = false;
    }
  }

  private extractOrgIdFromState(): string | null {
    if (!this.params?.data) return null;

    return this.params.data.orgId ||
      this.params.data.id ||
      this.params.data.organizationId ||
      this.params.data.orgID ||
      this.params.orgId ||
      this.params.id ||
      null;
  }

  private loadSchoolData(): void {
    if (!this.orgId) {
      console.error('School ID is missing. Navigation params:', this.params);
      this.toastr.error('School ID is missing');
      return;
    }

    // Try to load from cache first (for page refresh scenarios)
    const cachedData = localStorage.getItem(this.SCHOOL_DATA_KEY);
    if (cachedData) {
      try {
        const schoolData = JSON.parse(cachedData);

        this.populateForm(schoolData);
        return;
      } catch (error) {

        localStorage.removeItem(this.SCHOOL_DATA_KEY);
      }
    }

    // Load from API if no cache or cache is invalid

    this.ngxSpinnerService.show('globalSpinner');
    this.orgnaizationService.getAllOrg({
      orgId: this.orgId
    }).subscribe({
      next: (response: any) => {
        console.log('School data response:', response);
        if (response) {
          // Cache the school data for page refresh scenarios
          localStorage.setItem(this.SCHOOL_DATA_KEY, JSON.stringify(response));

          this.populateForm(response);
        } else {
          this.toastr.error('School data not found');
        }
      },
      error: (error) => {
        this.toastr.error(error.error?.message || 'Failed to load school data');

      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  private populateForm(schoolData: any): void {
    this.addNewSchoolForm.patchValue({
      orgTitle: schoolData.orgTitle,
      orgEmail: schoolData.orgEmail,
      orgPhone: schoolData.orgPhone,
      orgSpoc1FullName: schoolData.orgSpoc1FullName,
      orgSpoc1Email: schoolData.orgSpoc1Email,
      orgSpoc1Phone: schoolData.orgSpoc1Phone,
      orgAddress: schoolData.orgAddress,
      orgType: schoolData.orgType
    });
    this.menuTitle = 'Edit School';

  }

  get orgUserArray(): FormArray {
    return this.orgUserForm.get('user') as FormArray;
  }

  get departmentsArray(): FormArray {
    return this.departmentForm.get('deptNames') as FormArray;
  }

  get existingClassesArray(): FormArray {
    return this.existingClassesForm.get('existingClasses') as FormArray;
  }

  get existingUsersArray(): FormArray {
    return this.existingUsersForm.get('existingUsers') as FormArray;
  }

  addNewOrgUser(): FormGroup {
    return this.formBuilder.group({
      name: ['', Validators.required],
      userEmailId: ['', [Validators.required, this.emailValidator]],
      userPhoneNumber: ['', Validators.required],
      org: ['ORGANIZATION'], // Use ORGANIZATION instead of SCHOOL for API
      orguOrganizationId: [this.orgId],
      orguDepartmentIds: [[], Validators.required],
      isEditing: [true], // New users start in edit mode
    });
  }

  addNewClass(): FormGroup {
    return this.formBuilder.group({
      deptName: ['', Validators.required],
      isEditing: [true],
    });
  }

  addExistingClass(classData: any): FormGroup {
    return this.formBuilder.group({
      deptName: [classData.deptName, Validators.required],
      deptId: [classData.deptId],
      originalName: [classData.deptName],
      isEditing: [false],
    });
  }

  addExistingUser(userData: any): FormGroup {
    // Use consolidated department names array if available, otherwise fallback to single deptName
    const deptNameArray = userData.deptNames && userData.deptNames.length > 0
      ? userData.deptNames
      : (userData.deptName ? [userData.deptName] : []);



    return this.formBuilder.group({
      userId: [userData.userId], // Add user ID for delete operations
      userName: [userData.userName, Validators.required],
      userEmailId: [userData.userEmailId, [Validators.required, this.emailValidator]],
      userPhoneNumber: [userData.userPhoneNumber, [Validators.required, Validators.pattern(/^\d{10}$/)]],
      deptName: [deptNameArray], // Current selected departments (as names)
      deptIds: [userData.deptIds || []], // Store department IDs for API calls
      originalUserName: [userData.userName],
      originalUserEmailId: [userData.userEmailId],
      originalUserPhoneNumber: [userData.userPhoneNumber],
      originalDeptName: [deptNameArray], // Store as array to match current format
      originalDeptIds: [userData.deptIds || []], // Store original department IDs
      isEditing: [false],
    });
  }

  createUserRow(): void {
    this.orgUserArray.push(this.addNewOrgUser());
  }

  createClassRow(): void {
    this.departmentsArray.push(this.addNewClass());
  }

  private populateExistingClassesForm(departments: any[]): void {
    // Clear existing form array
    while (this.existingClassesArray.length !== 0) {
      this.existingClassesArray.removeAt(0);
    }

    // Add each department to the form array
    departments.forEach(dept => {
      this.existingClassesArray.push(this.addExistingClass(dept));
    });
  }

  private populateExistingUsersForm(users: any[]): void {
    // Clear existing form array
    while (this.existingUsersArray.length !== 0) {
      this.existingUsersArray.removeAt(0);
    }

    // Transform API response: consolidate users with multiple departments
    const consolidatedUsers = this.consolidateUserDepartments(users);

    // Add each consolidated user to the form array
    consolidatedUsers.forEach(user => {
      this.existingUsersArray.push(this.addExistingUser(user));
    });
  }

  /**
   * Consolidates user-department relationships from API response
   * Transforms multiple objects per user into single objects with department arrays
   */
  private consolidateUserDepartments(users: any[]): any[] {
    if (!users || users.length === 0) {
      return [];
    }

    console.log('Original API response (before consolidation):', users);

    // Group users by userId
    const userMap = new Map<string, any>();

    users.forEach(userDept => {
      const userId = userDept.userId;

      if (!userId) {
        console.warn('User object missing userId:', userDept);
        return;
      }

      if (userMap.has(userId)) {
        // User already exists, add department to arrays
        const existingUser = userMap.get(userId);

        // Add department ID if not already present
        if (userDept.deptId && !existingUser.deptIds.includes(userDept.deptId)) {
          existingUser.deptIds.push(userDept.deptId);
        }

        // Add department name if not already present
        if (userDept.deptName && !existingUser.deptNames.includes(userDept.deptName)) {
          existingUser.deptNames.push(userDept.deptName);
        }
      } else {

        const consolidatedUser = {
          userId: userDept.userId,
          userName: userDept.userName,
          userEmailId: userDept.userEmailId,
          userPhoneNumber: userDept.userPhoneNumber,
          deptIds: userDept.deptId ? [userDept.deptId] : [],
          deptNames: userDept.deptName ? [userDept.deptName] : [],

          deptId: userDept.deptId,
          deptName: userDept.deptName
        };

        userMap.set(userId, consolidatedUser);
      }
    });

    // Convert map to array
    const consolidatedUsers = Array.from(userMap.values());

    console.log('Consolidated users (after transformation):', consolidatedUsers);

    return consolidatedUsers;
  }

  toggleEditMode(index: number, formArrayName: string): void {
    let formGroup;
    if (formArrayName === 'departmentForm') {
      formGroup = this.departmentsArray.at(index);
    } else if (formArrayName === 'existingUsersForm') {
      formGroup = this.existingUsersArray.at(index);
    } else {
      formGroup = this.orgUserArray.at(index);
    }
    const isEditing = formGroup.get('isEditing')?.value;
    formGroup.patchValue({ isEditing: !isEditing });
  }


  isRowEditing(index: number, formArrayName: string): boolean {
    let formGroup;
    if (formArrayName === 'departmentForm') {
      formGroup = this.departmentsArray.at(index);
    } else if (formArrayName === 'existingUsersForm') {
      formGroup = this.existingUsersArray.at(index);
    } else {
      formGroup = this.orgUserArray.at(index);
    }
    console.log('formGroup', formGroup);
    console.log('formGroup.get(isEditing)', formGroup.get('isEditing')?.value);

    return formGroup.get('isEditing')?.value;
  }

  // Check if existing user is in edit mode
  isExistingUserEditing(index: number): boolean {
    const formGroup = this.existingUsersArray.at(index);
    return formGroup.get('isEditing')?.value || false;
  }

  // Helper method to compare arrays
  private arraysEqual(a: any[], b: any[]): boolean {
    if (!Array.isArray(a) || !Array.isArray(b)) return false;
    if (a.length !== b.length) return false;

    // Sort both arrays to compare regardless of order
    const sortedA = [...a].sort();
    const sortedB = [...b].sort();

    return sortedA.every((val, index) => val === sortedB[index]);
  }

  // Toggle edit mode for existing user
  toggleExistingUserEdit(index: number): void {
    const formGroup = this.existingUsersArray.at(index);
    const isEditing = formGroup.get('isEditing')?.value;
    formGroup.patchValue({ isEditing: !isEditing });
  }

  // Check if a class is selected for existing user
  isClassSelectedForExistingUser(userIndex: number, className: string): boolean {
    const userControl = this.existingUsersArray.at(userIndex);
    const selectedClasses = userControl.get('deptName')?.value;

    if (Array.isArray(selectedClasses)) {
      return selectedClasses.includes(className);
    }
    return selectedClasses === className;
  }

  // Handle class selection change for existing user
  onExistingUserClassChange(userIndex: number, className: string, event: any): void {
    const userControl = this.existingUsersArray.at(userIndex);
    const currentClasses = userControl.get('deptName')?.value || [];

    let updatedClasses: string[];
    if (Array.isArray(currentClasses)) {
      updatedClasses = [...currentClasses];
    } else {
      updatedClasses = currentClasses ? [currentClasses] : [];
    }

    if (event.target.checked) {
      // Add class if not already selected
      if (!updatedClasses.includes(className)) {
        updatedClasses.push(className);
      }
    } else {
      // Remove class if selected
      updatedClasses = updatedClasses.filter(cls => cls !== className);
    }

    // Update the form control and mark it as touched/dirty
    const deptNameControl = userControl.get('deptName');
    if (deptNameControl) {
      deptNameControl.patchValue(updatedClasses);
      deptNameControl.markAsTouched();
      deptNameControl.markAsDirty();
    }

    console.log(`Class selection changed for user ${userIndex}:`, {
      className,
      checked: event.target.checked,
      updatedClasses,
      controlTouched: deptNameControl?.touched,
      controlDirty: deptNameControl?.dirty
    });
  }

  // Check if a class is selected for new user
  isClassSelectedForNewUser(userIndex: number, deptId: string): boolean {
    const userControl = this.orgUserArray.at(userIndex);
    const selectedDeptIds = userControl.get('orguDepartmentIds')?.value || [];
    return selectedDeptIds.includes(deptId);
  }

  // Handle class selection change for new user
  onNewUserClassChange(userIndex: number, deptId: string, event: any): void {
    const userControl = this.orgUserArray.at(userIndex);
    const currentDeptIds = userControl.get('orguDepartmentIds')?.value || [];

    let updatedDeptIds: string[];
    if (Array.isArray(currentDeptIds)) {
      updatedDeptIds = [...currentDeptIds];
    } else {
      updatedDeptIds = currentDeptIds ? [currentDeptIds] : [];
    }

    if (event.target.checked) {
      // Add department ID if not already selected
      if (!updatedDeptIds.includes(deptId)) {
        updatedDeptIds.push(deptId);
      }
    } else {
      // Remove department ID if selected
      updatedDeptIds = updatedDeptIds.filter(id => id !== deptId);
    }

    userControl.get('orguDepartmentIds')?.patchValue(updatedDeptIds);
  }

  // Save existing user changes
  saveExistingUser(index: number): void {
    const userControl = this.existingUsersArray.at(index);
    if (!userControl) {
      this.toastr.error('Invalid user row');
      return;
    }

    // Mark this specific control as touched to show validation errors
    userControl.markAsTouched();
    userControl.get('userName')?.markAsTouched();
    userControl.get('userEmailId')?.markAsTouched();
    userControl.get('userPhoneNumber')?.markAsTouched();

    // Check if this specific row is invalid
    if (userControl.invalid) {
      this.toastr.error('Please fill in all required user fields');
      return;
    }

    const newUserName = userControl.get('userName')?.value?.trim();
    const newUserEmail = userControl.get('userEmailId')?.value?.trim();
    const newUserPhone = userControl.get('userPhoneNumber')?.value?.trim();
    const newDeptNameArray = userControl.get('deptName')?.value;

    const originalUserName = userControl.get('originalUserName')?.value;
    const originalUserEmail = userControl.get('originalUserEmailId')?.value;
    const originalUserPhone = userControl.get('originalUserPhoneNumber')?.value;
    const originalDeptName = userControl.get('originalDeptName')?.value;

    console.log('saveExistingUser - Current form values:', {
      newUserName,
      newUserEmail,
      newUserPhone,
      newDeptNameArray,
      originalUserName,
      originalUserEmail,
      originalUserPhone,
      originalDeptName,
      deptNameControlTouched: userControl.get('deptName')?.touched,
      deptNameControlDirty: userControl.get('deptName')?.dirty
    });

    if (!newUserName || !newUserEmail || !newUserPhone) {
      this.toastr.error('All user fields are required');
      return;
    }

    // Validate that at least one class is selected
    if (!Array.isArray(newDeptNameArray) || newDeptNameArray.length === 0) {
      this.toastr.error('Please select at least one class');
      return;
    }

    // Get selected department IDs by converting selected names to IDs
    const selectedDeptIds: string[] = [];
    if (Array.isArray(newDeptNameArray) && newDeptNameArray.length > 0) {
      newDeptNameArray.forEach(deptName => {
        const dept = this.departmentsData.find(d => d.deptName === deptName);
        if (dept && dept.deptId) {
          selectedDeptIds.push(dept.deptId);
        }
      });
    }

    // Validate that we found valid department IDs
    if (selectedDeptIds.length === 0) {
      this.toastr.error('Please select at least one class');
      return;
    }

    // For backward compatibility, also get the first department name
    const newDeptName = newDeptNameArray && newDeptNameArray.length > 0 ? newDeptNameArray[0] : '';

    // Prepare original dept name for comparison
    const originalDeptNameArray = Array.isArray(originalDeptName) ? originalDeptName : (originalDeptName ? [originalDeptName] : []);
    const originalFirstDeptName = originalDeptNameArray.length > 0 ? originalDeptNameArray[0] : '';

    // Check if any changes were made
    const hasChanges = newUserName !== originalUserName ||
      newUserEmail !== originalUserEmail ||
      newUserPhone !== originalUserPhone ||
      newDeptName !== originalFirstDeptName ||
      !this.arraysEqual(newDeptNameArray, originalDeptNameArray);

    if (!hasChanges) {
      // No changes made, just exit edit mode
      console.log('No changes detected, exiting edit mode');
      this.toggleExistingUserEdit(index);
      return;
    }

    console.log('Changes detected:', {
      nameChanged: newUserName !== originalUserName,
      emailChanged: newUserEmail !== originalUserEmail,
      phoneChanged: newUserPhone !== originalUserPhone,
      classChanged: newDeptName !== originalFirstDeptName,
      newDeptName,
      originalFirstDeptName,
      selectedDeptIds,
      newDeptNameArray,
      originalDeptNameArray
    });

    // Get the user ID for the update API
    const userId = userControl.get('userId')?.value;
    if (!userId) {
      this.toastr.error('User ID not found. Cannot update user.');
      return;
    }

    // Validate that we have organization ID
    if (!this.orgId) {
      this.toastr.error('Organization ID not found. Please refresh and try again.');
      return;
    }

    // Prepare the update payload with all selected departments
    const updatePayload = {
      name: newUserName,
      userEmailId: newUserEmail,
      userPhoneNumber: newUserPhone,
      deptName: newDeptName, // Keep for backward compatibility
      orguDepartmentIds: selectedDeptIds, // Send all selected department IDs
      orguOrganizationId: this.orgId
    };

    console.log(`Updating user ${userId} with payload:`, updatePayload);
    this.ngxSpinnerService.show('globalSpinner');

    // Call the update API
    this.orgnaizationService.updateOrgUser(userId, updatePayload).subscribe({
      next: (response: any) => {
        this.toastr.success(`User "${newUserName}" updated successfully`);
        console.log('User updated successfully:', response);

        // Update the original values and exit edit mode
        userControl.patchValue({
          originalUserName: newUserName,
          originalUserEmailId: newUserEmail,
          originalUserPhoneNumber: newUserPhone,
          originalDeptName: newDeptNameArray,
          originalDeptIds: selectedDeptIds,
          deptIds: selectedDeptIds,
          isEditing: false
        });

        // Reload users to reflect any changes from the server
        this.loadUsers();
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to update user");
        console.error('Error updating user:', err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  // Cancel existing user edit
  cancelExistingUserEdit(index: number): void {
    const userControl = this.existingUsersArray.at(index);
    const originalUserName = userControl.get('originalUserName')?.value;
    const originalUserEmail = userControl.get('originalUserEmailId')?.value;
    const originalUserPhone = userControl.get('originalUserPhoneNumber')?.value;
    const originalDeptName = userControl.get('originalDeptName')?.value;

    // originalDeptName is now stored as an array, so use it directly
    const originalDeptNameArray = Array.isArray(originalDeptName) ? originalDeptName : (originalDeptName ? [originalDeptName] : []);

    // Restore original values and exit edit mode
    userControl.patchValue({
      userName: originalUserName,
      userEmailId: originalUserEmail,
      userPhoneNumber: originalUserPhone,
      deptName: originalDeptNameArray,
      isEditing: false
    });
  }

  // Store user data for deletion confirmation
  userToDelete: { name: string; email: string; userId: string; index: number } | null = null;

  // Handle deleting existing user - show modal instead of browser confirm
  deleteExistingUser(index: number): void {
    const userControl = this.existingUsersArray.at(index);
    const userName = userControl.get('userName')?.value;
    const userEmail = userControl.get('userEmailId')?.value;
    const userId = userControl.get('userId')?.value;

    // Store user data for modal
    this.userToDelete = {
      name: userName,
      email: userEmail,
      userId: userId,
      index: index
    };

    // Show confirmation modal
    this.showModal('deleteUserModal');
  }

  // Confirm user deletion after modal confirmation
  confirmDeleteUser(): void {
    if (!this.userToDelete) {
      return;
    }

    const { name: userName, email: userEmail, userId } = this.userToDelete;

    console.log('Deleting user:', { userName, userEmail, userId });

    // Hide modal first
    this.hideModal('deleteUserModal');

    // Validate that we have a user ID
    if (!userId) {
      this.toastr.error('User ID not found. Cannot delete user.');
      this.userToDelete = null;
      return;
    }

    // Call the delete API
    this.ngxSpinnerService.show('globalSpinner');

    this.orgnaizationService.deleteOrgUser(userId).subscribe({
      next: (response) => {
        this.toastr.success(`User "${userName}" deleted successfully`);
        console.log('User deleted successfully:', response);

        // Reload users to reflect the changes
        this.loadUsers();

        // Clear the stored user data
        this.userToDelete = null;
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to delete user");
        console.error('Error deleting user:', err);

        // Clear the stored user data
        this.userToDelete = null;
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  // Cancel new user input - remove the row entirely
  cancelNewUser(index: number): void {
    this.orgUserArray.removeAt(index);
  }

  removeRow(index: number, formArrayName: string): void {
    let formArray: FormArray;

    if (formArrayName === 'departmentForm') {
      formArray = this.departmentsArray;
    } else if (formArrayName === 'orgUserForm') {
      formArray = this.orgUserArray;
    } else {
      console.error('Invalid form array name:', formArrayName);
      return;
    }

    // Remove the row at the specified index
    formArray.removeAt(index);
    console.log(`Removed row ${index} from ${formArrayName}. Remaining rows: ${formArray.length}`);

    // Show success message
    const entityType = formArrayName === 'departmentForm' ? 'class' : 'user';
    this.toastr.success(`${entityType.charAt(0).toUpperCase() + entityType.slice(1)} row removed successfully`);
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  // Pagination event handlers for classes (client-side pagination)
  onClassCurrentPageChanged = (currentOffset: number) => {
    this.classOffset = currentOffset;
    // No need to reload data for client-side pagination
  };

  onClassPageSizeChanged = (pageSizeChanged: number) => {
    this.classOffset = 1;
    this.classLimit = pageSizeChanged;
    // No need to reload data for client-side pagination
  };

  // Pagination event handlers for users (client-side pagination)
  onUserCurrentPageChanged = (currentOffset: number) => {
    this.userOffset = currentOffset;
    // No need to reload data for client-side pagination
  };

  onUserPageSizeChanged = (pageSizeChanged: number) => {
    this.userOffset = 1;
    this.userLimit = pageSizeChanged;
    // No need to reload data for client-side pagination
  };

  // Delete handlers for pagination components
  deleteClassRecord = (elementID: number) => {
    console.log('Delete class with ID:', elementID);

    // Find the class in departmentsData by deptId
    const classToDelete = this.departmentsData.find(dept => dept.deptId === elementID);
    if (!classToDelete) {
      this.toastr.error('Class not found');
      return;
    }

    // Show confirmation dialog
    if (confirm(`Are you sure you want to delete the class "${classToDelete.deptName}"?`)) {
      this.ngxSpinnerService.show('globalSpinner');

      this.orgnaizationService.deleteDepartment(elementID.toString()).subscribe({
        next: (response) => {
          this.toastr.success(`Class "${classToDelete.deptName}" deleted successfully`);
          console.log('Class deleted successfully:', response);

          // Reload departments to reflect the changes
          this.loadDepartments();
        },
        error: (err) => {
          this.toastr.error(err?.error?.message || "Failed to delete class");
          console.error('Error deleting class:', err);
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        }
      });
    }
  };

  deleteUserRecord = (elementID: number) => {
    console.log('Delete user with ID:', elementID);

    // Find the user in usersData by userId
    const userToDelete = this.usersData.find(user => user.userId === elementID);
    if (!userToDelete) {
      this.toastr.error('User not found');
      return;
    }

    // Show confirmation dialog
    if (confirm(`Are you sure you want to delete the user "${userToDelete.userName}"?`)) {
      this.ngxSpinnerService.show('globalSpinner');

      this.orgnaizationService.deleteOrgUser(elementID.toString()).subscribe({
        next: (response) => {
          this.toastr.success(`User "${userToDelete.userName}" deleted successfully`);
          console.log('User deleted successfully:', response);

          // Reload users to reflect the changes
          this.loadUsers();
        },
        error: (err) => {
          this.toastr.error(err?.error?.message || "Failed to delete user");
          console.error('Error deleting user:', err);
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        }
      });
    }
  };

  private loadDepartments(): void {
    // For class dropdown, we need orgId (not userId)
    if (!this.orgId) {
      console.log('No orgId available yet, skipping department load');
      this.departmentsData = [];
      this.isLoadingDepartments = false;
      return;
    }

    this.isLoadingDepartments = true;
    const params = {
      limit: 1000,
      offset: 0,
      userId: '',
      orgId: this.orgId.toString()
    };

    console.log('Loading departments with params:', params);
    this.orgnaizationService.getAllDepartments(params).subscribe({
      next: (response: any) => {
        console.log('Departments API response:', response);

        if (response && response.data && Array.isArray(response.data)) {
          // Store departments data for dropdown usage (all departments for dropdown)
          this.departmentsData = response.data;

          // Set total count for pagination (client-side pagination)
          this.totalClassRecords = response.data.length;

          console.log(`Departments loaded: ${this.departmentsData.length} classes found`, this.departmentsData);

          // Populate existing classes form array
          this.populateExistingClassesForm(response.data);
        } else {
          console.log('No departments found or invalid response format');
          this.departmentsData = [];
          this.totalClassRecords = 0;
          // Clear existing classes form when no departments are found
          this.populateExistingClassesForm([]);
        }
      },
      error: (error) => {
        console.error('Error loading departments:', error);
        this.departmentsData = [];
        this.totalClassRecords = 0;
        this.populateExistingClassesForm([]);
        this.isLoadingDepartments = false;
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.isLoadingDepartments = false;
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  private loadUsers(): void {
    if (!this.orgId) {
      console.log('No orgId available yet, skipping users load');
      this.usersData = [];
      this.isLoadingUsers = false;
      return;
    }

    this.isLoadingUsers = true;
    console.log('Loading users for orgId:', this.orgId);

    // Load all users for client-side pagination
    this.orgnaizationService.getUsersByOrgId({ orgId: this.orgId.toString() }).subscribe({
      next: (response: any) => {
        console.log('Users API response:', response);

        if (response && response.data && Array.isArray(response.data)) {
          this.usersData = response.data;

          // Set total count for pagination
          this.totalUserRecords = response.count || response.data.length;

          console.log(`Users loaded: ${this.usersData.length} users found`, this.usersData);

          // Populate existing users form array
          this.populateExistingUsersForm(response.data);
        } else {
          console.log('No users found or invalid response format');
          this.usersData = [];
          this.totalUserRecords = 0;

          this.populateExistingUsersForm([]);
        }
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.usersData = [];
        this.totalUserRecords = 0;
        this.populateExistingUsersForm([]);
        this.isLoadingUsers = false;
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.isLoadingUsers = false;
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  private loadRoles(): void {
    this.isLoadingRoles = true;
    console.log('Loading roles from API');

    this.dataTransferService.getAllRoles().subscribe({
      next: (response: any) => {
        console.log('Roles API response:', response);

        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.rolesData = response.data;
          console.log(`Roles loaded: ${this.rolesData.length} roles found`, this.rolesData);

          // Find the USER role ID
          const userRole = this.rolesData.find(role => role.name === 'USER');
          if (userRole) {
            this.userRoleId = userRole.id;
            console.log(`USER role found with ID: ${this.userRoleId}`);
          } else {
            console.warn('USER role not found in roles data');
            this.toastr.warning('USER role not found. Please contact administrator.');
          }
        } else {
          console.log('No roles found or invalid response format');
          this.rolesData = [];
          this.toastr.error('Failed to load user roles');
        }
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.rolesData = [];
        this.toastr.error('Failed to load user roles');
        this.isLoadingRoles = false;
      },
      complete: () => {
        this.isLoadingRoles = false;
      }
    });
  }

  onMetaDataSubmit() {
    this.addNewSchoolForm.markAllAsTouched();

    if (this.addNewSchoolForm.valid) {
      // Prepare payload with correct structure for both add and update
      const formValues = this.addNewSchoolForm.value;
      const postData = this.isEdit
        ? {
          // Payload structure for /organizations/update API
          orgId: this.orgId,
          orgTitle: formValues.orgTitle,
          orgEmail: formValues.orgEmail,
          orgPhone: formValues.orgPhone,
          orgType: "SCHOOL",
          orgSpoc1FullName: formValues.orgSpoc1FullName,
          orgSpoc1Email: formValues.orgSpoc1Email,
          orgSpoc1Phone: formValues.orgSpoc1Phone,
          orgAddress: formValues.orgAddress
        }
        : formValues;

      console.log(`${this.isEdit ? 'Updating' : 'Adding'} school with payload:`, postData);

      this.ngxSpinnerService.show('globalSpinner');
      const apiCall = this.isEdit
        ? this.orgnaizationService.updateOrganization(postData)
        : this.orgnaizationService.addOrganization(postData);

      apiCall.subscribe({
        next: (response: any) => {
          this.orgId = this.isEdit ? response.data.orgId : response.data.orgId;
          this.toastr.success(response.message);

          // Load departments and users after school is saved and orgId is available
          if (this.orgId) {
            this.loadDepartments();
            this.loadUsers();
          }
        },
        error: (err) => {
          this.toastr.error(err?.error?.message || 'Failed to save school information');
          console.error('Error adding or updating school:', err);
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    } else {
      this.toastr.error('Please fill all required fields correctly');
      return;
    }
  }
  saveClass(index: number) {
    // Validate that we have an orgId (school must be saved first)
    if (!this.orgId) {
      this.toastr.error('Please save the school information first before adding classes');
      return;
    }

    const classControl = this.departmentsArray.at(index);
    if (!classControl) {
      this.toastr.error('Invalid class row');
      return;
    }

    // Mark this specific control as touched to show validation errors
    classControl.markAsTouched();
    classControl.get('deptName')?.markAsTouched();

    // Check if this specific row is invalid
    if (classControl.invalid) {
      this.toastr.error('Please fill in the class name');
      return;
    }

    const deptName = classControl.get('deptName')?.value?.trim();
    if (!deptName) {
      this.toastr.error('Class name cannot be empty');
      return;
    }

    const payload = {
      deptOrganisationId: this.orgId,
      deptNames: [deptName]
    };

    console.log(`Saving class at index ${index} with payload:`, payload);
    this.ngxSpinnerService.show('globalSpinner');

    this.orgnaizationService.addDepartmets(payload).subscribe({
      next: (response) => {
        this.toastr.success(`Class "${deptName}" saved successfully`);
        console.log('Class saved successfully:', response);

        // Reload departments to include newly created class in real-time
        this.loadDepartments();

        // Remove the saved row from the add new section
        this.departmentsArray.removeAt(index);
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to save class");
        console.error('Error saving class:', err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  cancelNewClass(index: number) {
    // Remove the row entirely to return to state before "Add New Class" was clicked
    this.departmentsArray.removeAt(index);
  }

  saveClasses() {
    // Keep the original method for bulk save if needed elsewhere
    // Validate that we have an orgId (school must be saved first)
    if (!this.orgId) {
      this.toastr.error('Please save the school information first before adding classes');
      return;
    }

    // Mark all form controls as touched to show validation errors
    this.departmentForm.markAllAsTouched();

    // Check if form is invalid (this will catch empty required fields)
    if (this.departmentForm.invalid) {
      this.toastr.error('Please fill in all required class names');
      return;
    }

    const deptNames = this.departmentsArray.controls.map(control =>
      control.get('deptName')?.value?.trim()).filter(name => !!name);

    // Check if there are any valid class names to save
    if (deptNames.length === 0) {
      this.toastr.error('Please add at least one valid class name');
      return;
    }

    const payload = {
      deptOrganisationId: this.orgId, // Use the dynamic orgId from the current school
      deptNames: deptNames
    };

    console.log('Saving classes with payload:', payload);
    this.ngxSpinnerService.show('globalSpinner');

    this.orgnaizationService.addDepartmets(payload).subscribe({
      next: (response) => {
        this.toastr.success("Classes saved successfully");
        console.log('Classes saved successfully:', response);

        // Reload departments to include newly created ones in real-time
        this.loadDepartments();

        // Reset the department form to clear the input fields
        this.resetDepartmentForm();

        // Exit edit mode for all rows after successful save
        this.exitAllEditModes('departmentForm');
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to save classes");
        console.error('Error saving classes:', err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  private resetDepartmentForm(): void {
    // Clear the department form completely
    this.departmentForm = this.formBuilder.group({
      deptNames: this.formBuilder.array([]),
    });
  }

  private resetUserForm(): void {
    // Clear the user form completely
    this.orgUserForm = this.formBuilder.group({
      user: this.formBuilder.array([]),
    });
  }

  private exitAllEditModes(formType: string): void {
    // Exit edit mode for all rows after successful save
    if (formType === 'departmentForm') {
      this.departmentsArray.controls.forEach((_, index) => {
        if (this.isRowEditing(index, 'departmentForm')) {
          this.toggleEditMode(index, 'departmentForm');
        }
      });
    }
  }

  saveUser(index: number) {
    // Validate that we have an orgId (school must be saved first)
    if (!this.orgId) {
      this.toastr.error('Please save the school information first before adding users');
      return;
    }

    // Validate that we have the USER role ID
    if (!this.userRoleId) {
      this.toastr.error('User role not loaded. Please refresh the page and try again.');
      return;
    }

    const userControl = this.orgUserArray.at(index);
    if (!userControl) {
      this.toastr.error('Invalid user row');
      return;
    }

    // Mark this specific control as touched to show validation errors
    userControl.markAsTouched();
    if (userControl instanceof FormGroup) {
      Object.keys(userControl.controls).forEach(key => {
        userControl.get(key)?.markAsTouched();
      });
    }

    // Check if this specific row is invalid
    if (userControl.invalid) {
      this.toastr.error('Please fill in all required user fields');
      return;
    }

    // Get selected department IDs (should be an array)
    const selectedDepartmentIds = userControl.get('orguDepartmentIds')?.value;

    // Ensure orguDepartmentIds is an array
    let departmentIds: string[] = [];
    if (Array.isArray(selectedDepartmentIds)) {
      departmentIds = selectedDepartmentIds;
    } else if (selectedDepartmentIds) {
      // If it's a single value, convert to array
      departmentIds = [selectedDepartmentIds];
    }

    const userData = {
      name: userControl.get('name')?.value?.trim(),
      userEmailId: userControl.get('userEmailId')?.value?.trim(),
      userPhoneNumber: userControl.get('userPhoneNumber')?.value?.trim(),
      org: 'ORGANIZATION', // Use ORGANIZATION for API
      userRoleId: this.userRoleId, // Use dynamic role ID from API
      orguOrganizationId: this.orgId.toString(),
      orguDepartmentIds: departmentIds
    };

    // Validate required fields
    if (!userData.name || !userData.userEmailId || !userData.userPhoneNumber || !userData.orguDepartmentIds.length) {
      this.toastr.error('Please fill in all required fields for this user');
      return;
    }

    console.log(`Saving user at index ${index} with data:`, userData);
    this.ngxSpinnerService.show('globalSpinner');

    // Call the API to add organization user
    this.dataTransferService.addOrgUser(userData).subscribe({
      next: (response: any) => {
        this.toastr.success(`User "${userData.name}" saved successfully`);
        console.log('User saved successfully:', response);

        // Remove the saved row from the add new section
        this.orgUserArray.removeAt(index);

        // Reload users to show the newly added user in the existing users list
        this.loadUsers();
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to save user");
        console.error('Error saving user:', err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  saveUsers() {
    // Keep the original method for bulk save if needed elsewhere
    // Validate that we have an orgId (school must be saved first)
    if (!this.orgId) {
      this.toastr.error('Please save the school information first before adding users');
      return;
    }

    // Validate that we have the USER role ID
    if (!this.userRoleId) {
      this.toastr.error('User role not loaded. Please refresh the page and try again.');
      return;
    }

    if (this.orgUserForm.invalid) {
      this.orgUserForm.markAllAsTouched();
      this.toastr.error('Please fill all required fields correctly');
      return;
    }

    const users = this.orgUserArray.controls.map(control => {
      const selectedDepartmentIds = control.get('orguDepartmentIds')?.value;
      let departmentIds: string[] = [];
      if (Array.isArray(selectedDepartmentIds)) {
        departmentIds = selectedDepartmentIds;
      } else if (selectedDepartmentIds) {
        departmentIds = [selectedDepartmentIds];
      }

      return {
        name: control.get('name')?.value?.trim(),
        userEmailId: control.get('userEmailId')?.value?.trim(),
        userPhoneNumber: control.get('userPhoneNumber')?.value?.trim(),
        org: 'ORGANIZATION', // Ensure ORGANIZATION is used for API
        userRoleId: this.userRoleId,
        orguOrganizationId: this.orgId.toString(),
        orguDepartmentIds: departmentIds
      };
    }).filter(user => user.name && user.userEmailId && user.userPhoneNumber && user.orguDepartmentIds.length > 0);

    if (users.length === 0) {
      this.toastr.error('Please add at least one valid user');
      return;
    }

    console.log('Saving users with data:', users);
    this.ngxSpinnerService.show('globalSpinner');

    // Save users one by one using the addOrgUser API
    let savedCount = 0;
    let errorCount = 0;

    users.forEach((userData, index) => {
      this.dataTransferService.addOrgUser(userData).subscribe({
        next: (response: any) => {
          savedCount++;
          console.log(`User ${index + 1} saved successfully:`, response);

          if (savedCount + errorCount === users.length) {
            this.ngxSpinnerService.hide('globalSpinner');
            if (errorCount === 0) {
              this.toastr.success(`All ${savedCount} users saved successfully`);
              // Clear the user form and reload users to show newly added users
              this.resetUserForm();
              this.loadUsers();
            } else {
              this.toastr.warning(`${savedCount} users saved, ${errorCount} failed`);
              // Still reload users to show successfully saved ones
              this.loadUsers();
            }
          }
        },
        error: (err) => {
          errorCount++;
          console.error(`Error saving user ${index + 1}:`, err);
          this.toastr.error(`Failed to save user: ${userData.name}`);

          if (savedCount + errorCount === users.length) {
            this.ngxSpinnerService.hide('globalSpinner');
            if (savedCount === 0) {
              this.toastr.error('Failed to save all users');
            } else {
              this.toastr.warning(`${savedCount} users saved, ${errorCount} failed`);
              // Reload users to show successfully saved ones
              this.loadUsers();
            }
          }
        }
      });
    });
  }

  // Check if existing class is in edit mode
  isExistingClassEditing(index: number): boolean {
    const formGroup = this.existingClassesArray.at(index);
    return formGroup.get('isEditing')?.value || false;
  }

  // Toggle edit mode for existing class
  toggleExistingClassEdit(index: number): void {
    const formGroup = this.existingClassesArray.at(index);
    const isEditing = formGroup.get('isEditing')?.value;
    formGroup.patchValue({ isEditing: !isEditing });
  }

  // Save existing class changes
  saveExistingClass(index: number): void {
    const classControl = this.existingClassesArray.at(index);
    if (!classControl) {
      this.toastr.error('Invalid class row');
      return;
    }

    // Mark this specific control as touched to show validation errors
    classControl.markAsTouched();
    classControl.get('deptName')?.markAsTouched();

    // Check if this specific row is invalid
    if (classControl.invalid) {
      this.toastr.error('Please fill in the class name');
      return;
    }

    const newClassName = classControl.get('deptName')?.value?.trim();
    const originalName = classControl.get('originalName')?.value;
    const deptId = classControl.get('deptId')?.value;

    if (!newClassName) {
      this.toastr.error('Class name cannot be empty');
      return;
    }

    if (newClassName === originalName) {
      // No changes made, just exit edit mode
      this.toggleExistingClassEdit(index);
      return;
    }

    // Payload structure for /departments/update API
    // deptId: ID of the department to update
    // deptNames: Array of new class names (can contain multiple names)
    const payload = {
      deptId: deptId,
      deptNames: [newClassName]
    };

    console.log('Updating class with payload:', payload);
    this.ngxSpinnerService.show('globalSpinner');

    // Use the correct update endpoint
    this.orgnaizationService.updateDepartments(payload).subscribe({
      next: (response) => {
        this.toastr.success(`Class "${newClassName}" updated successfully`);
        console.log('Class updated successfully:', response);

        // Update the original name and exit edit mode
        classControl.patchValue({
          originalName: newClassName,
          isEditing: false
        });

        // Reload departments to reflect the changes
        this.loadDepartments();
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to update class");
        console.error('Error updating class:', err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  // Cancel existing class edit
  cancelExistingClassEdit(index: number): void {
    const classControl = this.existingClassesArray.at(index);
    const originalName = classControl.get('originalName')?.value;

    // Restore original name and exit edit mode
    classControl.patchValue({
      deptName: originalName,
      isEditing: false
    });
  }

  // Store class data for deletion confirmation
  classToDelete: { name: string; deptId: string; index: number } | null = null;

  // Handle deleting existing class - show modal instead of browser confirm
  deleteExistingClass(index: number): void {
    const classControl = this.existingClassesArray.at(index);
    const className = classControl.get('deptName')?.value;
    const deptId = classControl.get('deptId')?.value;

    // Store class data for modal
    this.classToDelete = {
      name: className,
      deptId: deptId,
      index: index
    };

    // Show confirmation modal
    this.showModal('deleteClassModal');
  }

  // Confirm deletion after modal confirmation
  confirmDeleteClass(): void {
    if (!this.classToDelete) {
      return;
    }

    const { name: className, deptId } = this.classToDelete;

    console.log('Deleting class:', { className, deptId });

    // Hide modal first
    this.hideModal('deleteClassModal');

    // Validate that we have a department ID
    if (!deptId) {
      this.toastr.error('Department ID not found. Cannot delete class.');
      this.classToDelete = null;
      return;
    }

    // Call the delete API
    this.ngxSpinnerService.show('globalSpinner');

    this.orgnaizationService.deleteDepartment(deptId).subscribe({
      next: (response) => {
        this.toastr.success(`Class "${className}" deleted successfully`);
        console.log('Class deleted successfully:', response);

        // Reload departments to reflect the changes
        this.loadDepartments();

        // Clear the stored class data
        this.classToDelete = null;
      },
      error: (err) => {
        this.toastr.error(err?.error?.message || "Failed to delete class");
        console.error('Error deleting class:', err);

        // Clear the stored class data
        this.classToDelete = null;
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      }
    });
  }

  // Helper method to check if a value is an array (for template use)
  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  // Helper method to get original index for pagination
  getOriginalIndex(item: any, array: any[] | undefined): number {
    if (!array) return 0;
    return array.indexOf(item);
  }

  // Row creation restriction methods for classes
  hasUnsavedClassRows(): boolean {
    return this.departmentsArray.length > 0;
  }

  hasUnsavedExistingClassRows(): boolean {
    return this.existingClassesArray.controls.some(control =>
      control.get('isEditing')?.value === true
    );
  }

  getClassButtonTooltip(): string {
    if (!this.orgId) {
      return 'Save school information first before adding classes';
    }
    if (this.hasUnsavedClassRows()) {
      return 'Please save or cancel the current class row before adding a new one';
    }
    if (this.hasUnsavedExistingClassRows()) {
      return 'Please save or cancel the existing class being edited before adding a new one';
    }
    return '';
  }

  // Row creation restriction methods for users
  hasUnsavedUserRows(): boolean {
    return this.orgUserArray.length > 0;
  }

  hasUnsavedExistingUserRows(): boolean {
    return this.existingUsersArray.controls.some(control =>
      control.get('isEditing')?.value === true
    );
  }

  getUserButtonTooltip(): string {
    if (!this.orgId) {
      return 'Save school information first';
    }
    if (this.departmentsData.length === 0) {
      return 'Add classes first before adding users';
    }
    if (this.hasUnsavedUserRows()) {
      return 'Please save or cancel the current user row before adding a new one';
    }
    if (this.hasUnsavedExistingUserRows()) {
      return 'Please save or cancel the existing user being edited before adding a new one';
    }
    return '';
  }

  // Modal utility methods
  showModal(modalId: string): void {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string): void {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }

    // Clear class data when modal is hidden
    if (modalId === 'deleteClassModal') {
      this.classToDelete = null;
    }
  }


}
