import { Component, OnInit } from '@angular/core';
import {
  FormBuilder
} from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { Constants, ModuleTypes } from 'src/app/config/constants';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';

@Component({
  selector: 'app-manage-playlist',
  templateUrl: './manage-playlist.component.html',
  styleUrls: ['./manage-playlist.component.scss']
})
export class ManagePlaylistComponent implements OnInit {
  totalNumberOfRecords: number = 0;
  offset: number = Constants.offset;
  limit: number = Constants.limit;
  menuTitle: string = 'Story Management';
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.RECOMMENDATION);
  _tomodule: string = ModuleTypes.RECOMMENDATION;
  searchTerm = '';
  isLargeScreen: boolean = true;
  languageData: any;
  genresData: any;
  ageGroupData: any;
  storyList = [];
  isEdit: boolean = false;
  editData: any;
  columns: any[] = [
    { title: 'Name of the story', dataKey: 'stTitle' },
    // { title: 'Author Name', dataKey: 'authorName' },
    { title: 'Genre', dataKey: 'stGenreName' },
    { title: 'Age group', dataKey: 'ageGroup' },
    { title: 'Languages', dataKey: 'sfLanguageName' },
  ];

  // columns: any[] = [
  //   { title: 'Name of the story', dataKey: 'stTitle' },
  //   { title: 'Genre', dataKey: 'stGenreName' },
  //   { title: 'Age group', dataKey: 'stAgeGroupName' },
  //   { title: 'Languages', dataKey: 'sfLanguageName' },
  //   { title: 'Tags', dataKey: 'stTags' },
  // ];
  recommendedStoryData = [];
  playlistId: number;

  // Class selection properties
  departmentsData: any[] = [];
  isLoadingDepartments: boolean = false;
  selectedClassId: string = '';
  selectedClassName: string = '';
  schoolId: string = '';
  schoolName: string = '';

  // Copy playlist modal properties
  showCopyModal: boolean = false;
  organizationsData: any[] = [];
  availablePlaylistsData: any[] = [];
  selectedOrgId: string = '';
  selectedOrgName: string = '';
  selectedPlaylistId: string = '';
  selectedPlaylistTitle: string = '';
  isLoadingOrganizations: boolean = false;
  isLoadingPlaylists: boolean = false;

  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    // Get school context from route parameters or navigation state
    this.route.queryParams.subscribe(params => {
      this.schoolId = params['schoolId'] || '1'; // Default to '1' for testing
      this.schoolName = params['schoolName'] || 'Demo School'; // Default name for testing

      if (this.schoolId) {
        this.menuTitle = `Manage Playlist - ${this.schoolName}`;
        this.loadDepartments();
      }
    });

    this.callAllListApis();
  }


  callAllListApis() {
    this.loadData();
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.loadData();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getStoryList(): Promise<any> {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset - 1,
      roleId: '',
    };

    this.ngxSpinnerService.show('globalSpinner');

    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllStories(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.totalNumberOfRecords = value.count;
            const storyData = value.data?.map((story: any) => {
              const sfLanguageName = Array.from(
                new Set(story?.storyFiles.map((file: any) => file.sfLanguageName))
              );
              const ageGroup = story.stAgeGroupName?.map((ageGroup: any) => ageGroup?.sm_entityname)
              return {
                ...story,
                sfLanguageName,
                ageGroup
              };
            });
            this.storyList = storyData;
            console.log("storyList", this.storyList);
            resolve(this.storyList);
          } else {
            resolve([]);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
          reject(err);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    });
  }

  getRecommendedStoryList(): Promise<any> {
    this.ngxSpinnerService.show('globalSpinner');

    return new Promise((resolve, reject) => {
      this.dataTransferService.getRecommendedStoryList().subscribe({
        next: (value: any) => {
          if (value.data) {
            this.playlistId = value?.data[0]?.plId;
            this.recommendedStoryData = value?.data[0]?.story?.map((story: any) => {
              const sfLanguageName = story?.storyFiles.map(
                (file: any) => file.sfLanguageName
              );
              return {
                ...story,
                sfLanguageName: sfLanguageName,
              };
            });
            resolve(this.recommendedStoryData);
          } else {
            resolve([]);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
          reject(err);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    });
  }

  async loadData() {
    try {
      const storyList = await this.getStoryList();
      const recommendedStoryList = await this.getRecommendedStoryList();

      const recommendedStIds = new Set(recommendedStoryList.map((story: any) => story.stId));
      this.storyList = storyList.map((story: any) => ({
        ...story,
        isAddBtn: !recommendedStIds.has(story.stId)
      }))

    } catch (error) {
      console.error("Error loading story data", error);
    }
  }


  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.loadData();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.loadData();
  };


  deleteRecord = (elementID: number) => {
    console.log('elementID', elementID);
  };

  // getAllLanguages(): Promise<any> {
  //   const payload = {
  //     limit: '',
  //     offset: '',
  //   };
  //   return new Promise((resolve, reject) => {
  //     this.dataTransferService.getAllLanguages(payload).subscribe({
  //       next: (value: any) => {
  //         if (value.data) {
  //           this.languageData = value.data;
  //           resolve(value.data);
  //         } else {
  //           resolve(null);
  //         }
  //       },
  //       error: (err) => {
  //         this.toastr.error(err.error.message);
  //         console.log(err);
  //         reject(err);
  //       },
  //     });
  //   });
  // }

  // getAllGenres(): Promise<any> {
  //   const payload = {
  //     limit: '',
  //     offset: '',
  //   };
  //   return new Promise((resolve, reject) => {
  //     this.dataTransferService.getAllGenres(payload).subscribe({
  //       next: (value: any) => {
  //         if (value.data) {
  //           this.genresData = value.data;
  //           console.log('genresData', this.genresData);
  //           resolve(value.data);
  //         } else {
  //           resolve(null);
  //         }
  //       },
  //       error: (err) => {
  //         this.toastr.error(err.error.message);
  //         console.log(err);
  //         reject(err);
  //       },
  //     });
  //   });
  // }

  // getAllAgeGroup(): Promise<any> {
  //   const payload = {
  //     limit: '',
  //     offset: '',
  //   };
  //   return new Promise((resolve, reject) => {
  //     this.dataTransferService.getAllAgeGroup(payload).subscribe({
  //       next: (value: any) => {
  //         if (value.data) {
  //           this.ageGroupData = value.data;
  //           resolve(value.data);
  //         } else {
  //           resolve(null);
  //         }
  //       },
  //       error: (err) => {
  //         this.toastr.error(err.error.message);
  //         console.log(err);
  //         reject(err);
  //       },
  //     });
  //   });
  // }


  showModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

  handleOpenModal(event: { modalId: string; isEdit: boolean; data?: any }) {
    this.isEdit = event.isEdit;
    // console.log("event.data;",event.data);
    this.editData = event.data;
    this.showModal(event.modalId);
  }

  // if (columnName === 'language') {
  //   this.commonModalHeader = 'Languages the story is available in :';
  //   this.commonModalData = Array.isArray(data.sfLanguageName) && data.sfLanguageName.length > 0
  //     ? data.sfLanguageName.join(', ')
  //     : 'No languages available';
  //     console.log('Modal Data:', this.data);
  // } else

  updateRecommendation() {
    const action = this.isEdit ? 'removeStoryIds' : 'addStoryIds';
    const postData = {
      plsPlaylistId: this.playlistId,
      [action]: [this.editData.stId],
    };

    this.ngxSpinnerService.show('globalSpinner');

    this.dataTransferService.updateRecommendation(postData).subscribe({
      next: (response: any) => {
        this.toastr.success(response?.message || 'Recommendation list updated');
        this.loadData();
        this.hideModal('recommendationModal');
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'An error occurred';
        this.toastr.error(errorMessage);
        console.error(error);
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  // Load departments/classes for the school
  private loadDepartments(): void {
    if (!this.schoolId) {
      console.log('No schoolId available, skipping department load');
      return;
    }

    this.isLoadingDepartments = true;
    const params = {
      limit: 1000, // High limit to get all classes for the school
      offset: 0,
      userId: '', // Send empty userId as specified
      orgId: this.schoolId.toString()
    };

    console.log('Loading departments for playlist with params:', params);

    // Create a mock API call for now - this would need to be replaced with actual OrgnizationService
    // For demonstration purposes, creating mock data
    setTimeout(() => {
      this.departmentsData = [
        { deptId: '1', deptName: 'Grade 1' },
        { deptId: '2', deptName: 'Grade 2' },
        { deptId: '3', deptName: 'Grade 3' },
        { deptId: '4', deptName: 'Grade 4' },
        { deptId: '5', deptName: 'Grade 5' }
      ];
      this.isLoadingDepartments = false;
      console.log(`Classes loaded for playlist: ${this.departmentsData.length} classes found`, this.departmentsData);
    }, 1000);

    // TODO: Replace with actual API call when OrgnizationService is properly injected
    // this.orgnaizationService.getAllDepartments(params).subscribe({
    //   next: (response: any) => {
    //     if (response && response.data) {
    //       this.departmentsData = response.data;
    //       console.log(`Classes loaded for playlist: ${this.departmentsData.length} classes found`, this.departmentsData);
    //     } else {
    //       this.departmentsData = [];
    //     }
    //   },
    //   error: (error) => {
    //     console.error('Error loading departments for playlist:', error);
    //     this.toastr.error('Failed to load classes');
    //     this.departmentsData = [];
    //   },
    //   complete: () => {
    //     this.isLoadingDepartments = false;
    //   }
    // });
  }

  // Handle class selection
  onClassSelected(classData: any): void {
    this.selectedClassId = classData?.deptId || '';
    this.selectedClassName = classData?.deptName || '';

    console.log('Class selected for playlist management:', {
      classId: this.selectedClassId,
      className: this.selectedClassName
    });

    // Update menu title to include class name
    if (this.selectedClassName) {
      this.menuTitle = `Manage Playlist - ${this.selectedClassName}`;
    }

    // Load playlist data for the selected class
    if (this.selectedClassId) {
      this.loadPlaylistForClass(this.selectedClassId);
    }
  }

  // Load playlist data for the selected class
  private loadPlaylistForClass(classId: string): void {
    // This method would load the specific playlist for the class
    // For now, we'll reload the general data
    this.loadData();
  }

  // Show copy playlist modal
  showCopyPlaylistModal(): void {
    if (!this.selectedClassId) {
      this.toastr.warning('Please select a class first');
      return;
    }

    this.showCopyModal = true;
    this.loadOrganizations();
    this.showModal('copyPlaylistModal');
  }

  // Load all organizations for the copy modal
  private loadOrganizations(): void {
    this.isLoadingOrganizations = true;

    // Mock data for demonstration - replace with actual API call
    setTimeout(() => {
      this.organizationsData = [
        { orgId: '1', orgTitle: 'ABC Elementary School' },
        { orgId: '2', orgTitle: 'XYZ High School' },
        { orgId: '3', orgTitle: 'Demo Middle School' },
        { orgId: '4', orgTitle: 'Test Academy' }
      ];
      this.isLoadingOrganizations = false;
      console.log('Organizations loaded for copy modal:', this.organizationsData.length);
    }, 800);

    // TODO: Replace with actual API call
    // this.dataTransferService.getAllSchools(params).subscribe({
    //   next: (response: any) => {
    //     if (response && response.data) {
    //       this.organizationsData = response.data;
    //       console.log('Organizations loaded for copy modal:', this.organizationsData.length);
    //     } else {
    //       this.organizationsData = [];
    //     }
    //   },
    //   error: (error) => {
    //     console.error('Error loading organizations:', error);
    //     this.toastr.error('Failed to load schools');
    //     this.organizationsData = [];
    //   },
    //   complete: () => {
    //     this.isLoadingOrganizations = false;
    //   }
    // });
  }

  // Handle organization selection in copy modal
  onOrganizationSelected(orgData: any): void {
    this.selectedOrgId = orgData?.orgId || '';
    this.selectedOrgName = orgData?.orgTitle || '';

    // Clear previous playlist selection
    this.selectedPlaylistId = '';
    this.selectedPlaylistTitle = '';
    this.availablePlaylistsData = [];

    if (this.selectedOrgId) {
      this.loadPlaylistsForOrganization(this.selectedOrgId);
    }
  }

  // Load playlists for selected organization
  private loadPlaylistsForOrganization(orgId: string): void {
    this.isLoadingPlaylists = true;

    // Mock data for demonstration - replace with actual API call
    setTimeout(() => {
      // Generate mock playlists based on selected organization
      const mockPlaylists = [
        { plId: `${orgId}_1`, playlistTitle: 'Adventure Stories', genreId: 1, genreName: 'Adventure' },
        { plId: `${orgId}_2`, playlistTitle: 'Mystery Tales', genreId: 2, genreName: 'Mystery' },
        { plId: `${orgId}_3`, playlistTitle: 'Comedy Collection', genreId: 3, genreName: 'Comedy' },
        { plId: `${orgId}_4`, playlistTitle: 'Educational Stories', genreId: 4, genreName: 'Educational' }
      ];

      this.availablePlaylistsData = mockPlaylists;
      this.isLoadingPlaylists = false;
      console.log('Playlists loaded for organization:', this.availablePlaylistsData.length);
    }, 600);

    // TODO: Replace with actual API call using PlaylistService
    // this.playlistService.getPlaylistsByEntityId(orgId).subscribe({
    //   next: (response: any) => {
    //     if (response && response.data) {
    //       this.availablePlaylistsData = response.data;
    //       console.log('Playlists loaded for organization:', this.availablePlaylistsData.length);
    //     } else {
    //       this.availablePlaylistsData = [];
    //     }
    //   },
    //   error: (error) => {
    //     console.error('Error loading playlists:', error);
    //     this.toastr.error('Failed to load playlists');
    //     this.availablePlaylistsData = [];
    //   },
    //   complete: () => {
    //     this.isLoadingPlaylists = false;
    //   }
    // });
  }

  // Handle playlist selection in copy modal
  onPlaylistSelected(playlistData: any): void {
    this.selectedPlaylistId = playlistData?.plId || '';
    this.selectedPlaylistTitle = playlistData?.playlistTitle || '';
  }

  // Copy stories from selected playlist
  copyPlaylistStories(): void {
    if (!this.selectedPlaylistId) {
      this.toastr.warning('Please select a playlist to copy');
      return;
    }

    this.ngxSpinnerService.show('globalSpinner');

    // Mock copying stories - replace with actual API call
    setTimeout(() => {
      const mockCopiedStories = [
        {
          stId: 101,
          stTitle: 'The Magic Forest Adventure',
          stGenreName: 'Adventure',
          ageGroup: ['6-8 years'],
          sfLanguageName: ['English', 'Hindi']
        },
        {
          stId: 102,
          stTitle: 'Mystery of the Lost Treasure',
          stGenreName: 'Mystery',
          ageGroup: ['8-10 years'],
          sfLanguageName: ['English']
        },
        {
          stId: 103,
          stTitle: 'The Funny Robot',
          stGenreName: 'Comedy',
          ageGroup: ['5-7 years'],
          sfLanguageName: ['English', 'Hindi', 'Tamil']
        }
      ];

      // Add copied stories to current playlist management
      this.processCopiedStories(mockCopiedStories);

      this.toastr.success(`Successfully copied ${mockCopiedStories.length} stories from ${this.selectedPlaylistTitle}`);
      this.hideCopyModal();
      this.loadData(); // Refresh the story list
      this.ngxSpinnerService.hide('globalSpinner');
    }, 1500);

    // TODO: Replace with actual API call using PlaylistService
    // this.playlistService.getPlaylistById(this.selectedPlaylistId).subscribe({
    //   next: (response: any) => {
    //     if (response && response.data && response.data.stories) {
    //       const copiedStories = response.data.stories;
    //       this.processCopiedStories(copiedStories);
    //       this.toastr.success(`Successfully copied ${copiedStories.length} stories from ${this.selectedPlaylistTitle}`);
    //       this.hideCopyModal();
    //       this.loadData();
    //     } else {
    //       this.toastr.warning('No stories found in the selected playlist');
    //     }
    //   },
    //   error: (error) => {
    //     console.error('Error copying playlist stories:', error);
    //     this.toastr.error('Failed to copy playlist stories');
    //   },
    //   complete: () => {
    //     this.ngxSpinnerService.hide('globalSpinner');
    //   }
    // });
  }

  // Process copied stories and add them to current playlist
  private processCopiedStories(stories: any[]): void {
    console.log('Processing copied stories:', stories);

    // Add copied stories to the recommended story data
    if (stories && stories.length > 0) {
      // Filter out stories that are already in the recommended list
      const existingStoryIds = new Set(this.recommendedStoryData.map((story: any) => story.stId));
      const newStories = stories.filter(story => !existingStoryIds.has(story.stId));

      if (newStories.length > 0) {
        // Add new stories to the recommended list
        this.recommendedStoryData = [...this.recommendedStoryData, ...newStories];

        // Update the story list to reflect that these stories are now recommended
        this.storyList = this.storyList.map((story: any) => ({
          ...story,
          isAddBtn: !existingStoryIds.has(story.stId) && !newStories.some(newStory => newStory.stId === story.stId)
        }));

        console.log(`Added ${newStories.length} new stories to recommended list`);

        // In a real implementation, this would also:
        // 1. Make API call to save the updated playlist
        // 2. Update the playlist on the server
        // 3. Handle any errors during the save process
      } else {
        this.toastr.info('All selected stories are already in the recommended list');
      }
    }
  }

  // Hide copy modal
  hideCopyModal(): void {
    this.hideModal('copyPlaylistModal');
    this.resetCopyModalData();
  }

  // Reset copy modal data
  private resetCopyModalData(): void {
    this.selectedOrgId = '';
    this.selectedOrgName = '';
    this.selectedPlaylistId = '';
    this.selectedPlaylistTitle = '';
    this.organizationsData = [];
    this.availablePlaylistsData = [];
    this.showCopyModal = false;
  }

}
