import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { ModuleTypes, UserTypes } from 'src/app/config/constants';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { Constants } from 'src/app/config/constants';

@Component({
  selector: 'app-portal-users',
  templateUrl: './portal-users.component.html',
  styleUrls: ['./portal-users.component.scss'],
})
export class PortalUsersComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent)
  listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: number = 0;
  offset: number = Constants.offset;
  columns: any[] = [
    { title: 'Name', dataKey: 'name' },
    { title: 'Email', dataKey: 'userEmailId' },
    { title: 'Mobile Number', dataKey: 'userPhoneNumber' },
    { title: 'Role', dataKey: 'roleName' },
  ];
  isEdit: boolean = false;
  portalUsers = [];
  isLargeScreen: boolean = true;
  limit: number = Constants.limit;
  menuTitle: string = 'Users';
  searchControl: FormControl = new FormControl();
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.PORTALUSERS);
  _tomodule: string = ModuleTypes.PORTALUSERS;
  searchTerm = '';
  modalData: void;
  addNewUserForm: FormGroup;
  rolesData=[]
  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder
  ) {
    this.addNewUserForm = this.formBuilder.group({
      name: ['', Validators.required],
      userEmailId: ['',[Validators.required,Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'),],],
      userPhoneNumber: [
        '', 
        [Validators.required, Validators.pattern('^[0-9]{10}$')] 
      ],
      userRoleId:['',Validators.required],
      userStatus:['ACTIVE'],
      org: [UserTypes.ADMIN],
    });
  }

  ngOnInit(): void {
    this.getAllPortalUsers();
    this.checkScreenSize();
    this.getAllRoles();
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getAllPortalUsers();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getAllPortalUsers() {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset - 1,
      userRole:UserTypes.ADMIN+','+UserTypes.STORYTELLER,
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllUsers(payload).subscribe({
      next: (value: any) => {
          this.totalNumberOfRecords = value?.count;
          this.portalUsers = value?.Data.map((user:any)=>({
            ...user,
            roleName:user?.roles.map((role:any)=>role.name),
          }));
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getAllRoles() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllRoles().subscribe({
      next: (value: any) => {
        let excludeId=2;
         this.rolesData=value.data.filter((role:any)=>excludeId!=role.id);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getAllPortalUsers();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.getAllPortalUsers();
  };

  deleteRecord = (elementID: any) => {
    console.log('elementID', elementID);
  };

  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }

  showModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
    this.addNewUserForm.reset();
  }

  handleOpenModal(event: { modalId: string; isEdit: boolean; data?: any }) {
    this.isEdit = event.isEdit;
    this.addNewUserForm.patchValue(event.data);
    this.showModal(event.modalId);
  }

  onSubmit() {
    this.addNewUserForm.markAllAsTouched();
    if(this.addNewUserForm.valid){
    this.addNewPortalUser();
    }
    else{
      this.toastr.error('Please fill all required fields correctly');
    }
  }

  getErrorMsg(formField: string, label: string): string {
    const control = this.addNewUserForm.controls[formField];

    if (control?.hasError('required')) {
      return `${label} is required.`;
    }

    if (control?.hasError('pattern')) {
      return `Please enter a valid ${label}.`;
    }

    return '';
  }

  addNewPortalUser() {
    // this.ngxSpinnerService.show('globalSpinner');  
    this.dataTransferService.addNewPortalUser(this.addNewUserForm.value).subscribe({
      next: (value: any) => {
        this.toastr.success(value.message);
        this.ngxSpinnerService.hide('globalSpinner');
        this.hideModal('addNewUserModal');
        this.getAllPortalUsers();
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

}
