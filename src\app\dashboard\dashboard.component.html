<app-sidebar [menuTitle]="menuTitle">
  <div class="content-wrapper fade-in">
    <div *ngIf="!isAdmin" class="row mt-0">
      <div [ngClass]="{'col-12': isSmallScreen(),'col-md-6': isMediumScreen(),'col-lg-4': isLargeScreen()}" class="mb-4"
        *ngFor="let card of storytellerCards">
        <div class="card info-card katha-card">
          <div class="card-body cardContainer">
            <div class="head p-2">
              <p>
                <a class="countNumbers">
                  <ng-container *ngIf="isNumber(card.count); else stringFormat">
                    {{ card.count | number: '1.0-0' }}
                  </ng-container>
                  <ng-template #stringFormat>{{ card.count }}</ng-template></a>
              </p>
              <span class="countTitle">{{ card.title }}</span>
            </div>
            <div class="icon">
              <img class="menu-icons" [src]="card.iconSrc" style="height: 50px; width: 50px;" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="isAdmin" class="row mt-0">
      <div [ngClass]="{'col-12': isSmallScreen(),'col-md-6': isMediumScreen(),'col-lg-4': isLargeScreen()}" class="mb-4"
        *ngFor="let card of adminCards">
        <div class="card info-card katha-card">
          <div class="card-body cardContainer">
            <div class="head p-2">
              <p>
                <a class="countNumbers">
                  <ng-container *ngIf="isNumber(card.count); else stringFormat">
                    {{ card.count | number: '1.0-0' }}
                  </ng-container>
                  <ng-template #stringFormat>{{ card.count }}</ng-template>
                </a>
              </p>
              <span class="countTitle">{{ card.title }}</span>
            </div>
            <div class="icon">
              <img class="menu-icons" [src]="card.iconSrc" style="height: 50px; width: 50px;" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="isAdmin" class="row mt-4">
      <div class="col-lg-12">
        <div class="card shadow-sm chart-card katha-card py-3">
          <div class="row chart-row">
            <div class="col-lg-12">
              <h4 class="text-center mt-2"></h4>
              <div class="filter-options d-flex justify-content-between align-items-center px-3">
                <div class="text-group insight-text-group">
                  <input type="radio" id="all" name="timeframe" value="all" (change)="onframeChange($event)" checked>
                  <label for="all" class="text-label">Months</label>

                  <input type="radio" id="3-months" name="timeframe" value="3 months" (change)="onframeChange($event)">
                  <label for="3-months" class="text-label">3 Months</label>
                </div>

                <div class="text-group insight-text-group">
                  <input type="radio" id="users" name="userType" value="users" (change)="onTimeframeChange($event)"
                    checked>
                  <label style="text-decoration: none !important;" for="users" class="text-label">Users</label>

                  <!-- <input type="radio" id="bookClubs" name="userType" value="bookClubs"
                    (change)="onTimeframeChange($event)">
                  <label for="bookClubs" class="text-label">Bookclubs</label> -->
                </div>
              </div>

              <p-chart class="canvas mt-4" type="line" height="300px" [data]="basicData"
                [options]="basicOptions"></p-chart>
            </div>
          </div>

        </div>
      </div>
    </div>

    <div *ngIf="isAdmin" class="row  mt-5 katha-card shadow-sm py-2 mx-1">
      <!-- Pie Chart Column -->
      <div class="col-md-6 ">
        <h5 class="text-center ">User Subscription Status</h5>
        <p-chart type="pie" [data]="userPieChartData" [options]="userPieChartOptions">
        </p-chart>
      </div>
      <!-- Bar Chart Column -->
      <div class="col-md-6 d-flex flex-column">
        <h5 class="text-center ">Subscriptions Overview</h5>
        <!-- Added custom class 'bar-chart-container' -->
        <p-chart class="bar-chart-container" type="bar" [data]="subscriptionBarChartData"
          [options]="subscriptionBarChartOptions">
        </p-chart>
      </div>
    </div>
    <!-- <div *ngIf="isAdmin" class="row mt-4 katha-card shadow-sm py-2">
      <div class="col-md-6 d-flex flex-column align-items-center justify-content-center" style="height: 400px;">
        <h5 class="text-center mb-3">User Subscription Status</h5>
        <p-chart
          type="pie"
          [data]="userPieChartData"
          [options]="userPieChartOptions"
          style="max-width: 100%; width: 100%; height: 300px;">
        </p-chart>
      </div>
      <div class="col-md-6 d-flex flex-column align-items-center justify-content-center" style="height: 400px;">
        <h5 class="text-center mb-3">Subscriptions Overview</h5>
        <p-chart
          type="bar"
          [data]="subscriptionBarChartData"
          [options]="subscriptionBarChartOptions"
          style="max-width: 100%; width: 100%; height: 300px;">
        </p-chart>
      </div>
    </div> -->


    <div *ngIf="isAdmin" class="card mt-5 katha-card">
      <div class="card-title heading-text mb-1">Top 3 Storytellers Based on Listening Hours</div>
      <div class="card-body px-0 pt-0">
        <div class="table-responsive">
          <table class="table  table-sm mb-0">
            <thead>
              <tr>
                <th>
                  Name
                </th>
                <th class="text-right">
                  Total Listening hours
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let storyTeller of topStoryTellers">
                <td class="wrap-text">
                  {{storyTeller.storytellerName}}
                </td>
                <td class="text-right">
                  {{storyTeller.formattedListeningTime}}
                </td>
              </tr>
            </tbody>

          </table>
        </div>

      </div>
    </div>

    <div *ngIf="!isAdmin" class="card mt-5 katha-card">
      <div class="card-title heading-text mb-1">
        Top 3 Stories Based on Listening Hours
      </div>

      <div class="card-body px-0 pt-0">
        <div class="table-responsive">
          <table class="table table-sm mb-0">
            <thead>
              <tr>
                <th>
                  Name of the story
                </th>

                <th class="text-right">
                  Listening hours
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let story of storyBasedOnHours">
                <td class="wrap-text-1">
                  {{ story.storyTitle }}
                </td>

                <td class="text-right">
                  {{ story.totalListeningHours }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div *ngIf="!isAdmin" class="card mt-5 katha-card">
      <div class="card-title heading-text mb-1">
        Top 3 Stories Based on Ratings
      </div>

      <div class="card-body px-0 pt-0">
        <div class="table-responsive">
          <table class="table table-sm mb-0">
            <thead>
              <tr>
                <th>
                  Name of the story
                </th>

                <th class="text-right">
                  Average rating </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let story of storyBasedOnRatings">
                <td class="wrap-text-1">
                  {{ story.storyTitle }}
                </td>

                <td class="text-right">
                  {{ story.averageRating }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>


  </div>
</app-sidebar>