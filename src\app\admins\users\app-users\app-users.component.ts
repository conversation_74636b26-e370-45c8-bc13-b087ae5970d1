import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { Constants, ModuleTypes, UserTypes } from 'src/app/config/constants';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
@Component({
  selector: 'app-app-users',
  templateUrl: './app-users.component.html',
  styleUrls: ['./app-users.component.scss']
})
export class AppUsersComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent)
  listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: number = 0;
  addSubscriptionPlanForm: FormGroup;
  offset: number = Constants.offset;
  columns: any[] = [
    { title: 'Name', dataKey: 'name' },
    { title: 'Email', dataKey: 'userEmailId' },
    { title: 'Subscription Status', dataKey: 'subStatus' },
    { title: 'Subscription Plan', dataKey: 'subTitle' },
    // { title: 'Last Login Date', dataKey: 'lastLoginDate' },
    { title: 'Lock/Unlock', dataKey: 'userStatus' },

  ];
  isEdit: boolean = false;
  appUsers =[];
  isLargeScreen: boolean = true;
  limit: number = Constants.limit;
  menuTitle: string = 'Users';
  searchControl: FormControl = new FormControl();
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.APPUSERS);
  _tomodule: string = ModuleTypes.APPUSERS;
  searchTerm = '';
  modalData: void;
  activeLabel='Lock';
  inactiveLabel='Unlock';
  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder
  ) {
    this.addSubscriptionPlanForm = this.formBuilder.group({
      planName: ['', Validators.required],
      duration: ['', Validators.required],
      regularPrice: ['', Validators.required],
      offerPrice: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.getAllAppUsers();
    this.checkScreenSize();
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getAllAppUsers();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getAllAppUsers() {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset - 1,
      userRole: UserTypes.APP
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllUsers(payload).subscribe({
      next: (value: any) => {
        if (value.Data) {
          this.totalNumberOfRecords = value.count;
          this.appUsers = value?.Data;
        }
        // this.totalNumberOfRecords = 3;
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getAllAppUsers();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.getAllAppUsers();
  };

  deleteRecord = (elementID: any) => {
    console.log('elementID', elementID);
  };

  showModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
    this.addSubscriptionPlanForm.reset();
  }

  handleOpenModal(event: { modalId: string; isEdit: boolean; data?: any }) {
    this.isEdit = event.isEdit;
    this.addSubscriptionPlanForm.patchValue(event.data);
    this.showModal(event.modalId);
  }

  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }
  
  lockUnlockUser = (userData: any) => {
    const postData={
      userId:userData.userId,
      userStatus:userData.userStatus=='LOCKED'?'ACTIVE':'LOCKED'
    }
    this.ngxSpinnerService.show('globalSpinner');    
    this.dataTransferService.lockUnlockUser(postData).subscribe({
      next: (value: any) => {
        this.toastr.success(value.message);
        this.ngxSpinnerService.hide('globalSpinner');
        this.hideModalInPagination('lockUnlockModal')
        this.getAllAppUsers();
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  };
}
