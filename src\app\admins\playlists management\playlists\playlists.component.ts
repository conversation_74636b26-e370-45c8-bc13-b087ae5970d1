import { Component, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { Constants, ModuleTypes, PlaylistTypes } from 'src/app/config/constants';
import { PlaylistService } from 'src/app/shared/services/playlist.service';

@Component({
  selector: 'app-playlists',
  templateUrl: './playlists.component.html',
  styleUrls: ['./playlists.component.scss']
})


export class PlaylistsComponent implements OnInit {
  totalNumberOfRecords: number = 0;
  offset: number = Constants.offset;
  limit: number = Constants.limit;
  menuTitle: string = 'Playlists';
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.PLAYLISTS);
  _tomodule: string = ModuleTypes.PLAYLISTS;
  searchTerm = '';
  isLargeScreen: boolean = true;
  playlistsData = [];
  columns: any[] = [
    { title: 'Name', dataKey: 'title' },
    //  { title: 'Genre', dataKey: 'stGenreName' },
    //  { title: 'Age group', dataKey: 'ageGroup' },
    //  { title: 'Languages', dataKey: 'sfLanguageName' },
  ];

  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private plasylistService: PlaylistService,
  ) { }

  ngOnInit(): void {
    this.getAllPlaylists()
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getAllPlaylists()
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getAllPlaylists(): Promise<any> {

    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset - 1,
      playlistType: PlaylistTypes.PUBLIC,
    };


    this.ngxSpinnerService.show('globalSpinner');

    return new Promise((resolve, reject) => {
      this.plasylistService.getAllPlaylists(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.totalNumberOfRecords = value.count;
            this.playlistsData = value.data;
            resolve(this.playlistsData);
          } else {
            resolve([]);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
          reject(err);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    });
  }


  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getAllPlaylists()
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.getAllPlaylists()
  };


  deleteRecord = (elementID: number) => {
    console.log('elementID', elementID);
  };



  //  showModal(modalId: string) {
  //    const modal = document.getElementById(modalId);
  //    if (modal != null) {
  //      modal.style.display = 'block';
  //    }
  //  }

  //  hideModal(modalId: string) {
  //    const modal = document.getElementById(modalId);
  //    if (modal != null) {
  //      modal.style.display = 'none';
  //    }
  //  }


}
