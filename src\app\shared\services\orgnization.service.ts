import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class OrgnizationService {

  constructor(private readonly http: HttpClient) {

  }
  addOrganization(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/organizations/add`, payload);
  }

  updateOrganization(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/organizations/update`, payload);
  }

  addDepartmets(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/departments/add-department`, payload);
  }

  updateDepartments(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/departments/update`, payload);
  }

  getAllOrg(params: { orgId?: string | number }) {
    return this.http.get(`${environment.kathaMehfilUrl}/organizations/getById?`, { params });
  }

  getAllDepartments(params: { limit: number; offset: number; userId: string; orgId: string }) {
    return this.http.get(`${environment.kathaMehfilUrl}/departments/all`, { params });
  }

  getUsersByOrgId(params: { orgId: string; limit?: number; offset?: number }) {
    const queryParams: any = { orgId: params.orgId };

    // Add pagination parameters if provided
    if (params.limit !== undefined) {
      queryParams.limit = params.limit.toString();
    }
    if (params.offset !== undefined) {
      queryParams.offset = params.offset.toString();
    }

    return this.http.get(`${environment.kathaMehfilUrl}/organizations/usersByOrgId`, {
      params: queryParams
    });
  }

  deleteOrgUser(userId: string) {
    return this.http.post(`${environment.kathaMehfilUrl}/users/delete-org-user/${userId}`, {});
  }

  updateOrgUser(userId: string, payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/users/update-org-user/${userId}`, payload);
  }

  deleteDepartment(deptId: string) {
    return this.http.delete(`${environment.kathaMehfilUrl}/departments/delete`, {
      params: { deptId }
    });
  }

}
