import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { enrichStoryData, getBreadCrumbModules } from 'src/app/config/commonHelper';
import { ModuleTypes } from 'src/app/config/constants';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { Constants } from 'src/app/config/constants';
@Component({
  selector: 'app-recommendation',
  templateUrl: './recommendation.component.html',
  styleUrls: ['./recommendation.component.scss']
})
export class RecommendationComponent implements OnInit {
  totalNumberOfRecords: number = 0;
  offset: number = Constants.offset;
  limit: number = Constants.limit;
  menuTitle: string = 'Story Management';
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.RECOMMENDATION);
  _tomodule: string = ModuleTypes.RECOMMENDATION;
  searchTerm = '';
  isLargeScreen: boolean = true;
  languageData: any;
  genresData: any;
  ageGroupData: any;
  storyList:any = [];
  isEdit: boolean = false;
  editData: any;
  columns: any[] = [
    { title: 'Name of the story', dataKey: 'stTitle' },
    // { title: 'Author Name', dataKey: 'authorName' },
    { title: 'Genre', dataKey: 'stGenreName' },
    { title: 'Age group', dataKey: 'ageGroup' },
    { title: 'Languages', dataKey: 'sfLanguageName' },
  ];

  // columns: any[] = [
  //   { title: 'Name of the story', dataKey: 'stTitle' },
  //   { title: 'Genre', dataKey: 'stGenreName' },
  //   { title: 'Age group', dataKey: 'stAgeGroupName' },
  //   { title: 'Languages', dataKey: 'sfLanguageName' },
  //   { title: 'Tags', dataKey: 'stTags' },
  // ];
  recommendedStoryData:any[]= [];
  playlistId: number;
  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.loadData();
  }




  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.loadData();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getStoryList(): Promise<any> {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset - 1,
      roleId:'',
    };
  
    this.ngxSpinnerService.show('globalSpinner');
  
    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllStories(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.totalNumberOfRecords = value.count;
            /* const storyData = value.data?.map((story: any) => {
              const sfLanguageName = Array.from(
                new Set(story?.storyFiles.map((file: any) => file.sfLanguageName))
              );
              const ageGroup = story.stAgeGroupName?.map((ageGroup:any)=>ageGroup?.sm_entityname)
              return {
                ...story,
                sfLanguageName,
                ageGroup
              };
            });
            this.storyList = storyData; */
            this.storyList = enrichStoryData(value.data);
            resolve(this.storyList);
          } else {
            resolve([]);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
          reject(err);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    });
  }
  
  getRecommendedStoryList(): Promise<any> {
    this.ngxSpinnerService.show('globalSpinner');
  
    return new Promise((resolve, reject) => {
      this.dataTransferService.getRecommendedStoryList().subscribe({
        next: (value: any) => {
          if (value.data) {
            this.playlistId = value?.data[0]?.plId;
           /*  this.recommendedStoryData = value?.data[0]?.story?.map((story: any) => {
              const sfLanguageName = story?.storyFiles?.map(
                (file: any) => file?.sfLanguageName
              );
              return {
                ...story,
                sfLanguageName: sfLanguageName,
              };
            }); */
            this.recommendedStoryData = enrichStoryData(value?.data[0]?.story);
            resolve(this.recommendedStoryData);
          } else {
            resolve([]);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
          reject(err);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    });
  }
  
  async loadData() {
    try {
      const storyList = await this.getStoryList();  
      const recommendedStoryList = await this.getRecommendedStoryList();

      const recommendedStIds=new Set(recommendedStoryList?.map((story:any)=>story?.stId));
      this.storyList=storyList?.map((story:any)=>({
        ...story,
        isAddBtn:recommendedStIds.size>0? !recommendedStIds.has(story?.stId):true,
      }))
      
    } catch (error) {
      console.error("Error loading story data", error);
    }
  }
  
    
  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.loadData();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.loadData();
  };


  deleteRecord = (elementID: number) => {
    console.log('elementID', elementID);
  }; 

  showModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

  handleOpenModal(event: { modalId: string; isEdit: boolean; data?: any }) {
    this.isEdit = event.isEdit;
    // console.log("event.data;",event.data);
    this.editData = event.data;
    this.showModal(event.modalId);
  }

  // if (columnName === 'language') {
  //   this.commonModalHeader = 'Languages the story is available in :';
  //   this.commonModalData = Array.isArray(data.sfLanguageName) && data.sfLanguageName.length > 0 
  //     ? data.sfLanguageName.join(', ')
  //     : 'No languages available';
  //     console.log('Modal Data:', this.data);
  // } else

  updateRecommendation() {
    const action = this.isEdit ? 'removeStoryIds' : 'addStoryIds';
    const postData = {
      plsPlaylistId: this.playlistId,
      [action]: [this.editData.stId],
    };
  
    this.ngxSpinnerService.show('globalSpinner');
  
    this.dataTransferService.updateRecommendation(postData).subscribe({
      next: (response: any) => {
        this.toastr.success(response?.message || 'Recommendation list updated');
        this.loadData();
        this.hideModal('recommendationModal');
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (error) => {
        const errorMessage = error?.error?.message || 'An error occurred';
        this.toastr.error(errorMessage);
        console.error(error);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }
  
}
