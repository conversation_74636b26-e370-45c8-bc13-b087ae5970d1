.pagination {
  display: flex;
  justify-content: flex-end;
}

.page-numbers {
  padding-right: 1rem;
  background-position: calc(100% - 10px) center;
  background-repeat: no-repeat;
}

.dropdown-wrapper {
  position: relative;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: none;
  border: 1px solid #2D2D2D !important;
}

#perPageItems {
  padding-right: 40px !important;
}

.downArrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 14px;
}


@media (max-width: 768px) {
  .pagination {
    display: flex;
    justify-content: start;
    margin-top: 20px;
  }

  .page-numbers {
    margin-top: 10px;
  }

  .custom-pagination {
    margin-left: 15px;
    margin-right: 15px;
  }

  .downArrow {
    top: 60%;
  }

}

td,
th,
.tablePaddingx {
  padding-left: 28px !important;
  padding-right: 28px !important;
}

.card-title {
  border-bottom: 1px solid #333333;
}