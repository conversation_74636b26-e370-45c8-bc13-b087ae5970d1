import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DataTransferService {
  private rolesSubject = new BehaviorSubject<any>(this.getStoredRoles());
  constructor(private http: HttpClient, private router: Router) { }


  ///////////////////////////////////////////////////////

  //KATHA MEHFIL API

  ////////////////////////////////////////////////////////

  emailVerification(payload: any) {
    return this.http.post(
      `${environment.kathaMehfilUrl}/auth/email-verification`,
      payload
    );
  }

  submitOtp(payload: any) {
    return this.http.post(
      `${environment.kathaMehfilUrl}/auth/pass-code-verify`,
      payload
    );
  }

  logout(emailId: string, deviceId: string) {
    return this.http.get(`${environment.kathaMehfilUrl}/auth/signout`, { params: { emailId, deviceId, } });
  }

  getRolesAction(roles: any) {
    const sortedRoles = roles.sort((a: any, b: any) => a.id - b.id);
    this.setRoles(roles);
  }

  setRoles(roles: any) {
    localStorage.setItem('userRoles', JSON.stringify(roles));
    this.rolesSubject.next(roles);
  }

  private getStoredRoles() {
    const storedRoles = localStorage.getItem('userRoles');
    try {
      return storedRoles ? JSON.parse(storedRoles) : null;
    } catch (error) {
      console.error('Error parsing stored roles:', error);
      return null;
    }
  }


  getRoles() {
    return this.rolesSubject.asObservable();
  }

  getAllSubscriptions(payload: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/subscriptions/all`, {
      params: {
        isActive: payload.isActive,
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }

  addNewSubscriptionPlan(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/subscriptions/create`, payload);
  }

  updateSubscriptionPlan(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/subscriptions/update`, payload);
  }

  getAllStories(payload: any) {
    const params: any = {
      search: payload.search,
      limit: payload.limit,
      offset: payload.offset,
      roleId: payload.roleId
    };

    if (payload.status) {
      params.status = payload.status;
    }

    return this.http.get(`${environment.kathaMehfilUrl}/story/getAll`, {
      params: params,
    });
  }


  getStoryById(id: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/story/getById`, {
      params: {
        storyId: id
      },
    });
  }

  getRecommendedStoryList() {
    return this.http.get(`${environment.kathaMehfilUrl}/recomendation/list`);
  }

  updateRecommendation(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/recomendation/update`, payload);
  }

  getAllLanguages(payload: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/public/languages/getAllLang`, {
      params: {
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }

  getAllGenres(payload: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/public/genres/getAllGenres`, {
      params: {
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }

  getAllAgeGroup(payload: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/public/ageGroup/getAllAgeGroup`, {
      params: {
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }

  addStoryMetaData(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/story/add`, payload);
  }

  updateStoryMetaData(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/story/update`, payload);
  }

  addStoryFile(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/storyFiles/addStoryFile`, payload);
  }

  updateStoryFile(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/storyFiles/updateStoryFile`, payload);
  }

  getAllUsers(payload: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/users/list`, {
      params: {
        search: payload?.search,
        limit: payload?.limit,
        offset: payload?.offset,
        userRole: payload?.userRole
      },
    });
  }

  getAllRoles() {
    return this.http.get(`${environment.kathaMehfilUrl}/roles/list`);
  }

  addNewPortalUser(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/auth/signup`, payload);
  }

  addOrgUser(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/users/add-org-user`, payload);
  }

  lockUnlockUser(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/users/lock`, payload);
  }

  getDashboardCount() {
    return this.http.get(`${environment.kathaMehfilUrl}/dashboard/count`);
  }

  getChartData(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/dashboard/dashboard-details`, payload);
  }

  getTopRatingData() {
    return this.http.get(`${environment.kathaMehfilUrl}/dashboard/top-three`);
  }

  getAllSchools(payload: any) {
    const params: any = {
      limit: payload.limit,
      offset: payload.offset,
    };


    if (payload.orgTitle) {
      params.orgTitle = payload.orgTitle;
    }

    return this.http.get(`${environment.kathaMehfilUrl}/organizations/getAllOrg`, {
      params: params,
    });
  }
}
