<div class="custom-multiselect" [class.readonly]="readonly" (blur)="markAsTouched()">
  <div class="dropdown-trigger" (click)="toggleDropdown()">
    <span class="selected-item">
      {{ selected ? selected[labelKey] : placeholder }}
    </span>
    <span *ngIf="!readonly" class="arrow-icon" [class.open]="showDropdown">&#9660;</span>
  </div>

  <div class="options-dropdown" *ngIf="showDropdown">
    <div
      *ngFor="let option of options"
      class="option-item"
      (click)="onOptionSelect(option)"
    >
      <span>{{ option[labelKey] }}</span>
    </div>
  </div>
</div>
