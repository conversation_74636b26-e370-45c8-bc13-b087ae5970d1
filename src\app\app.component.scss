.dot-anim {
    display: inline-block;
    opacity: 0;
    animation: dotAppear 1.5s infinite;
  }
  
  .dot-anim:nth-child(1) {
    animation-delay: 0s;
  }
  
  .dot-anim:nth-child(2) {
    animation-delay: 0.3s;
  }
  
  .dot-anim:nth-child(3) {
    animation-delay: 0.6s;
  }
  
  @keyframes dotAppear {
    0%, 40% {
      opacity: 0;
    }
    50%, 100% {
      opacity: 1;
    }
  }
  

  @keyframes pulse {
    0%, 100% {
      transform: scale(1); /* Original size */
    }
    50% {
      transform: scale(1.2); /* Scale up to 120% */
    }
  }
  
  .spinner-pulse {
    animation: pulse 1.5s ease-in-out infinite; /* Duration and infinite repeat */
  }
  