import { Component, OnInit, After<PERSON><PERSON>w<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { DataTransferService } from '../shared/services/data-transfer.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs';
import { Constants, UserTypes } from '../config/constants';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('otp1Input') otp1Input!: ElementRef;

  loginForm: FormGroup;
  otpForm: FormGroup;
  isOtpSent: boolean = false;
  timer: number = 300;
  interval: any;
  canResend: boolean = false;
  userEmailId: string = '';
  otpToken: any;
  invalidOtp: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private router: Router,
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService
  ) {}

  ngOnInit(): void {
    const token=localStorage.getItem('token');
    if(token){
      this.router.navigate(['/dashboard']);
    }
    this.initializeForms();
  }

  ngAfterViewInit(): void {
    if (this.isOtpSent && this.otp1Input) {
      this.otp1Input.nativeElement.focus();
    }
  }

  ngOnDestroy(): void {
    clearInterval(this.interval);
  }

  initializeForms(): void {
    this.loginForm = this.formBuilder.group({
      userEmailId: [
        '',
        [
          Validators.required,
          Validators.pattern('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'),
        ],
      ],
      portalType:[UserTypes.ADMIN],
    });

    this.otpForm = this.formBuilder.group({
      otp1: ['', [Validators.required, Validators.pattern('[0-9]')]],
      otp2: [{ value: '', disabled: true }, [Validators.required, Validators.pattern('[0-9]')]],
      otp3: [{ value: '', disabled: true }, [Validators.required, Validators.pattern('[0-9]')]],
      otp4: [{ value: '', disabled: true }, [Validators.required, Validators.pattern('[0-9]')]],
      otp5: [{ value: '', disabled: true }, [Validators.required, Validators.pattern('[0-9]')]],
      otp6: [{ value: '', disabled: true }, [Validators.required, Validators.pattern('[0-9]')]],
    });

    this.otpForm.get('otp1')?.enable();
  }

  onKeyUp(event: KeyboardEvent, currentIndex: number): void {
    const currentInput = `otp${currentIndex}`;
    const nextInput = `otp${currentIndex + 1}`;
    const prevInput = `otp${currentIndex - 1}`;
    const input = event.target as HTMLInputElement;

    if (!/^[0-9]$/.test(input.value)) {
        input.value = '';
    }

    if (event.key >= '0' && event.key <= '9') {
        this.otpForm.get(currentInput)?.setValue(event.key); 
        this.otpForm.get(nextInput)?.enable();
        const nextElement = document.querySelector(`input[formControlName="${nextInput}"]`) as HTMLInputElement;
        if (nextElement) nextElement.focus();
    } else if (event.key === 'Backspace') {
        this.otpForm.get(currentInput)?.setValue('');

        if (currentIndex > 1) {
            this.otpForm.get(currentInput)?.disable();
            const prevElement = document.querySelector(`input[formControlName="${prevInput}"]`) as HTMLInputElement;

            if (prevElement) {
                prevElement.focus();
                setTimeout(() => {
                    prevElement.select();
                }, 0);
            }
        }
    }
}

  onSendOtp(): void {
    if (this.loginForm.invalid) {
      this.toastr.error('Please enter a valid email address.');
      return;
    }
    this.sendOtp();
  }

  sendOtp(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.ngxSpinnerService.show('globalSpinner');
      this.dataTransferService.emailVerification(this.loginForm.value).pipe(finalize(() => this.ngxSpinnerService.hide('globalSpinner'))).subscribe(
          (res: any) => {
            if (res.statusCode === 200 && res.data?.token) {
              this.otpToken = res.data.token;
              this.toastr.success(res.message || 'OTP sent successfully!');
              this.isOtpSent = true;
              this.startTimer();
  
              setTimeout(() => {
                if (this.otp1Input) {
                  this.otp1Input.nativeElement.focus();
                }
              }, 100);
  
              resolve(); 
            } else {
              const errorMessage = res.message || 'Unexpected error occurred.';
              this.toastr.error(errorMessage);
              reject(new Error(errorMessage)); 
            }
          },
          (error) => {
            const errorMessage = error?.error?.message || 'An error occurred. Please try again.';
            this.toastr.error(errorMessage);
            reject(new Error(errorMessage)); 
          }
        );
    });
  }

  
  
  // onSubmitOtp(): void {
  //   if (this.otpForm.invalid) {
  //     this.toastr.error('Please fill in all six OTP digits correctly.');
  //     return;
  //   }

  //   const otp = Object.values(this.otpForm.value).join('');
  //   const data = {
  //     userEmailId: this.loginForm.get('userEmailId')?.value,
  //     token: this.otpToken,
  //     passcode: otp,
  //     device: 'Unknown Device',
  //     ipAddress: 'Unknown',
  //     location: 'Unknown',
  //   };

  //   this.ngxSpinnerService.show('globalSpinner');
  //   this.dataTransferService
  //     .submitOtp(data)
  //     .pipe(
  //       finalize(() => this.ngxSpinnerService.hide('globalSpinner'))
  //     )
  //     .subscribe(
  //       (res: any) => {
  //         if (res.statusCode === 200 && res.data?.token) {
  //           localStorage.setItem('token', res.data?.token);
  //           localStorage.setItem('userId', res.data?.userId);
  //           localStorage.setItem('userName', res.data?.userName);
  //           localStorage.setItem('logedInUserEmailId', res.data?.userEmailId);
  //           this.dataTransferService.getRolesAction(res.data?.roleActions);
  //           localStorage.setItem('roleId', res.data?.roleActions[0]?.roleId);
  //           this.toastr.success(res.message || 'Login successful');
  //           this.router.navigate(['/dashboard']);
  //         } else {
  //         this.canResend = true;
  //         clearInterval(this.interval);
  //         this.timer = 300;
  //           this.toastr.error(res.message || 'Unexpected error occurred.');
  //         }
  //       },
  //       (error) => {
  //         if (error?.error?.message === 'Error: Invalid pass code') {
  //           this.invalidOtp = true;
  //         }
  //         this.canResend = true;
  //         clearInterval(this.interval);
  //         this.timer = 300;
  //         this.toastr.error(
  //           error?.error?.message || 'An error occurred. Please try again.'
  //         );
  //       }
  //     );
  // }

  startTimer(): void {
    this.timer = 300; 
    this.interval = setInterval(() => {
      if (this.timer > 0) {
        this.timer--;
      } else {
        this.canResend = true;
        clearInterval(this.interval);
      }
    }, 1000);
  }
  
  get formattedTimer(): string {
    const minutes = Math.floor(this.timer / 60);
    const seconds = this.timer % 60;
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }

  
  async onResendOtp(): Promise<void> {
    try {
      await this.sendOtp(); 
      this.timer = 300;
      this.canResend = false;
      this.invalidOtp = false;
      this.showModal('commonModal'); 
      this.otpForm.reset(); 
    } catch (error) {
      console.error('Error resending OTP:');
    }
  }
  

  onCancelOtp(): void {
    this.isOtpSent = false;
    this.canResend = false;
    this.invalidOtp = false;
    this.timer = 300;
    clearInterval(this.interval);
    this.otpForm.reset();
    Object.keys(this.otpForm.controls).forEach((key, index) => {
      this.otpForm.get(key)?.disable();
      if (index === 0) this.otpForm.get('otp1')?.enable();
    });
  }

  onPaste(event: ClipboardEvent): void {
    event.preventDefault(); 
    const clipboardData = event.clipboardData || (window as any).clipboardData;
    const pastedData = clipboardData.getData('text');
  
    if (pastedData.length === 6 && /^\d{6}$/.test(pastedData)) {
      const otpArray = pastedData.split('');
      otpArray.forEach((digit: any, index: number) => {
        const controlName = `otp${index + 1}`;
        this.otpForm.get(controlName)?.setValue(digit);
        this.otpForm.get(controlName)?.enable();
      });
  
      const lastInput = document.querySelector(
        `input[formControlName="otp6"]`
      ) as HTMLInputElement;
  
      if (lastInput) {
        lastInput.focus(); 
      }
    } else {
      this.toastr.error('Please paste a valid 6-digit OTP.');
    }
  }

  showModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
  }


onSubmitOtp(): void {
  if (this.otpForm.invalid) {
    this.toastr.error('Please fill in all six OTP digits correctly.');
    return;
  }

  const otp = Object.values(this.otpForm.value).join('');
  const deviceId = Constants.deviceId; 
  localStorage.setItem('deviceId',deviceId);

  const data = {
    userEmailId: this.loginForm.get('userEmailId')?.value,
    token: this.otpToken,
    passcode: otp,
    device: deviceId, 
    ipAddress: 'Unknown',
    location: 'Unknown',
  };

  this.ngxSpinnerService.show('globalSpinner');
  this.dataTransferService
    .submitOtp(data)
    .pipe(finalize(() => this.ngxSpinnerService.hide('globalSpinner')))
    .subscribe(
      (res: any) => {
        if (res.statusCode === 200 && res.data?.token) {
          localStorage.setItem('token', res.data?.token);
          localStorage.setItem('userId', res.data?.userId);
          localStorage.setItem('userName', res.data?.userName);
          localStorage.setItem('logedInUserEmailId', res.data?.userEmailId);
          this.dataTransferService.getRolesAction(res.data?.roleActions);
          localStorage.setItem('roleId', res.data?.roleActions[0]?.roleId);
          this.toastr.success(res.message || 'Login successful');
          this.router.navigate(['/dashboard']);
        } else {
          this.canResend = true;
          clearInterval(this.interval);
          this.timer = 300;
          this.toastr.error(res.message || 'Unexpected error occurred.');
        }
      },
      (error) => {
        if (error?.error?.message === 'Error: Invalid pass code') {
          this.invalidOtp = true;
        }
        this.canResend = true;
        clearInterval(this.interval);
        this.timer = 300;
        this.toastr.error(
          error?.error?.message || 'An error occurred. Please try again.'
        );
      }
    );
}

// getDeviceId(): string {
//   try {
//     let storedDeviceId = '';

//     const hasLocalStorage = this.isStorageAvailable('localStorage');
//     const hasSessionStorage = this.isStorageAvailable('sessionStorage');

//     if (hasLocalStorage) {
//       storedDeviceId = localStorage.getItem('device_id') || '';
//     }
    
//     if (!storedDeviceId && hasSessionStorage) {
//       storedDeviceId = sessionStorage.getItem('device_id') || '';
//     }

//     if (!storedDeviceId) {
//       storedDeviceId = this.generateSafeDeviceId();

//       if (hasLocalStorage) {
//         localStorage.setItem('device_id', storedDeviceId);
//       }
      
//       if (hasSessionStorage) {
//         sessionStorage.setItem('device_id', storedDeviceId);
//       }
//     }

//     return storedDeviceId;
//   } catch (error) {
//     console.error('Unexpected error while generating device ID:', error);
//     return 'Unknown Device';
//   }
// }

// generateSafeDeviceId(): string {
//   try {
//     const ua = navigator.userAgent || 'UnknownBrowser';
//     const screenSize = screen?.width && screen?.height ? `${screen.width}x${screen.height}` : 'UnknownScreen';
//     const randomId = this.generateUUID();

//     return `${screenSize}-${randomId}`;
//   } catch (error) {
//     console.error('Error generating device ID:', error);
//     return 'Fallback-Device-ID';
//   }
// }

// isStorageAvailable(type: 'localStorage' | 'sessionStorage'): boolean {
//   try {
//     const storage = window[type];
//     const testKey = '__test';

//     storage.setItem(testKey, '1');
//     storage.removeItem(testKey);

//     return true;
//   } catch {
//     return false;
//   }
// }

// generateUUID(): string {
//   return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
//     const r = (Math.random() * 16) | 0;
//     const v = c === 'x' ? r : (r & 0x3) | 0x8;
//     return v.toString(16);
//   });
// }

}
