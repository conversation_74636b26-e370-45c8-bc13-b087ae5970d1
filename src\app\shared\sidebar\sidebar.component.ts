import { Component, Input, OnInit } from '@angular/core';
import { IsActiveMatchOptions, NavigationEnd, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { DataTransferService } from '../services/data-transfer.service';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit {
  @Input() menuTitle: string;
  userRole: number = 0;
  currentYear: Date = new Date();
  // accessMenuList: any = [
  //   {
  //     portalType: 'ADMIN',
  //     route: '/dashboard',
  //     roleId: 1,
  //     name: 'Dashboard',
  //     roleName: 'ADMIN',
  //     icon: './assets/icons/Dashboard.svg',
  //     id: 1,
  //     menu: true,
  //   },
  //   {
  //     portalType: 'ADMIN',
  //     route: '/admins/plan-list',
  //     roleId: 1,
  //     name: 'Subscriptions',
  //     roleName: 'ADMIN',
  //     icon: './assets/icons/Subscriptions.svg',
  //     id: 2,
  //     menu: true,
  //   },
  //   {
  //     portalType: 'ADMIN',
  //     route: '/admins/stories',
  //     roleId: 1,
  //     name: 'Manage Stories',
  //     roleName: 'ADMIN',
  //     icon: './assets/icons/Stories.svg',
  //     id: 3,
  //     menu: true,
  //   },
  //   {
  //     portalType: 'ADMIN',
  //     route: '/admins/app-users',
  //     roleId: 1,
  //     name: 'Users',
  //     roleName: 'ADMIN',
  //     icon: './assets/icons/Users.svg',
  //     id: 4,
  //     menu: true,
  //     subMenu: [
  //       {
  //         name: 'App Users',
  //         route: '/admins/app-users',
  //         icon: './assets/icons/Users.svg',
  //       },
  //       {
  //         name: 'Portal Users',
  //         route: '/admins/portal-users',
  //         icon: './assets/icons/Users.svg',
  //       },
  //     ],
  //   },
  // ];
  accessMenuList: any = [];

  constructor(
    private dataTransferService: DataTransferService,
    public router: Router,
    private ngxSpinnerService: NgxSpinnerService,
    private toastr: ToastrService,

  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.resetSidebar();
        this.updateMenuStateBasedOnRoute();
      }
    });
  }

  ngOnInit(): void {
    this.updateMenuStateBasedOnRoute();
    this.getRoles();
  }

  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
      sidebar.classList.toggle('active');
    }
  }

  resetSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar && sidebar.classList.contains('active')) {
      sidebar.classList.remove('active');
    }
  }

  isMenuActive(route: string): boolean {
    const matchOptions: IsActiveMatchOptions = {
      paths: 'subset',
      queryParams: 'ignored',
      matrixParams: 'ignored',
      fragment: 'ignored',
    };

    return this.router.isActive(route, matchOptions);
  }

  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  toggleSubMenu(menu: any) {
    if (menu.subMenu) {
      menu.isOpen = !menu.isOpen;
    }
  }

  isParentMenuActive(menu: any): boolean {
    if (menu.subMenu) {
      return menu.subMenu.some((subMenu: any) =>
        this.router.isActive(subMenu.route, {
          paths: 'subset',
          queryParams: 'ignored',
          matrixParams: 'ignored',
          fragment: 'ignored',
        })
      );
    }
    return this.router.isActive(menu.route, {
      paths: 'subset',
      queryParams: 'ignored',
      matrixParams: 'ignored',
      fragment: 'ignored',
    });
  }

  isSubmenuActive(route: string): boolean {
    return this.router.isActive(route, {
      paths: 'exact',
      queryParams: 'ignored',
      matrixParams: 'ignored',
      fragment: 'ignored',
    });
  }

  updateMenuStateBasedOnRoute() {
    const currentRoute = this.router.url;
    this.accessMenuList.forEach((menu: any) => {
      if (menu.subMenu) {
        menu.isOpen = menu.subMenu.some(
          (subMenu: any) => subMenu.route === currentRoute
        );
      }
    });
  }

  logout() {
    this.ngxSpinnerService.show('globalSpinner');
    const email = localStorage.getItem('logedInUserEmailId');
    const deviceId = localStorage.getItem('deviceId');
    if (email && deviceId) {
      this.dataTransferService.logout(email, deviceId).subscribe({
        next: () => {
          this.ngxSpinnerService.hide('globalSpinner');
          localStorage.clear();
          this.router.navigate(['']);
        },
        error: (err) => {
          console.error('Logout failed', err);
          this.toastr.error('Logout failed')
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        }
      });
    } else {
      localStorage.clear();
      this.router.navigate(['/login']);
    }
  }


  showModal() {
    const modal = document.getElementById('logOutModal');
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal() {
    const modal = document.getElementById('logOutModal');
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

  getActiveIcon(defaultIcon: string): string {
    return defaultIcon.replace('.svg', '_Tan.svg');
  }

  getIcon(menu: any): string {
    if (this.isTouchDevice()) {
      return this.isMenuActive(menu.route) || this.isParentMenuActive(menu)
        ? this.getActiveIcon(menu.icon)
        : menu.icon;
    } else {
      return menu.isHovered || this.isMenuActive(menu.route) || this.isParentMenuActive(menu)
        ? this.getActiveIcon(menu.icon)
        : menu.icon;
    }
  }


  onHover(menu: any): void {
    if (!this.isTouchDevice()) {
      menu.isHovered = true;
    }
  }

  onMouseOut(menu: any): void {
    if (!this.isTouchDevice()) {
      menu.isHovered = false;
    }
  }

  getRoles() {
    this.dataTransferService.getRoles().subscribe((res: any) => {
      res.forEach((element: any) => {
        if (element.menu == true) {
          this.accessMenuList.push(element);
        }
      });

      const schoolMenu = {
        route: "/school-list",
        roleId: 1,
        name: "Manage School",
        roleName: "ADMIN",
        icon: "./assets/icons/SchoolManagment.svg",
        id: 1000,
        menu: true,
        isHovered: false
      }

      const playListMenu = {
        route: "/playlists",
        roleId: 1,
        name: "Playlists",
        roleName: "ADMIN",
        icon: "./assets/icons/SchoolManagment.svg",
        id: 1000,
        menu: true,
        isHovered: false
      };



      this.accessMenuList.push(playListMenu);
      this.accessMenuList.push(schoolMenu);

    })
  }
}
