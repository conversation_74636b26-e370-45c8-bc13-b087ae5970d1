<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div class="mt-0">
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div>


        <div class="add-new-playlist">
            <span class="heading-text">
                <span *ngIf="!isFromSchoolManagement">{{isEdit?'Edit Playlist':'Add New Playlist'}}</span>
                <span *ngIf="isFromSchoolManagement && !selectedClassName">{{isEdit?'Edit Playlist':'Add New
                    Playlist'}}</span>
                <span *ngIf="isFromSchoolManagement && selectedClassName">{{isEdit?'Edit Playlist':'Add Playlist'}} for
                    {{selectedClassName}}</span>
            </span>
            <form class="forms-sample mt-5" [formGroup]="addNewPlaylistForm">
                <div class="row mb-3 katha-card py-3 mx-0 px-0">
                    <!-- Class dropdown - only show when in school context (full width row at top) -->
                    <div class="col-lg-4 mb-5" *ngIf="isFromSchoolManagement">
                        <label class="form-label required-field" for="classId">Class</label>
                        <app-single-select-dropdown formControlName="classId" [options]="departmentsData"
                            [labelKey]="'deptName'" [valueKey]="'deptId'"
                            [placeholder]="isLoadingDepartments ? 'Loading classes...' : 'Select a class'"
                            [readonly]="isLoadingDepartments" [wantFullData]="true"
                            (selectionChange)="onClassSelected($event)">
                        </app-single-select-dropdown>
                        <div *ngIf="addNewPlaylistForm.controls['classId']?.invalid && addNewPlaylistForm.controls['classId']?.touched"
                            class="error-msg mt-3">
                            <div *ngIf="addNewPlaylistForm.controls['classId'].errors?.required">Class selection is
                                required.
                            </div>
                        </div>
                        <div class="mt-2" style="font-size: 12px; color: #6c757d;">
                            <i class="fa fa-info-circle me-1"></i>
                            This playlist will be specific to the selected class
                        </div>
                    </div>

                    <!-- Force new row for title and genre -->
                    <div class="w-100" *ngIf="isFromSchoolManagement"></div>

                    <!-- Playlist title -->
                    <div class="col-lg-6 mb-1">
                        <app-input-wrapper formControlName="playlistName" id="playlistName"
                            label="Title of the playlist" type="text" [isRequired]="true"
                            [isErrored]="addNewPlaylistForm.controls['playlistName']?.invalid && (addNewPlaylistForm.controls['playlistName']?.touched)"
                            [errorMessage]="'Title is required.'"></app-input-wrapper>
                    </div>

                    <!-- Genre -->
                    <div class="col-lg-4 mb-5">
                        <label class="form-label required-field" for="genreId">Genre</label>
                        <app-single-select-dropdown formControlName="genreId" [options]="genresData"
                            (selectionChange)="onGenreSelected($event)" [labelKey]="'title'" [valueKey]="'genId'"
                            [wantFullData]="true">
                        </app-single-select-dropdown>
                        <div *ngIf="addNewPlaylistForm.controls['genreId']?.invalid && addNewPlaylistForm.controls['genreId']?.touched"
                            class="error-msg mt-3">
                            <div *ngIf="addNewPlaylistForm.controls['genreId'].errors?.required">Genre is required.
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <div class="row" *ngIf="playlistStories&&playlistStories.length > 0">
                <div class="col-md-12">
                    <div class="card katha-card">
                        <div class="card-title tablePaddingx heading-text mb-1 py-3">
                            <span *ngIf="!isFromSchoolManagement">
                                {{addNewPlaylistForm.get('playlistName')?.value?addNewPlaylistForm.get('playlistName')?.value:'Playlist'}}
                            </span>
                            <span *ngIf="isFromSchoolManagement && selectedClassName">
                                Stories for {{selectedClassName}}
                                <span *ngIf="addNewPlaylistForm.get('playlistName')?.value"> -
                                    {{addNewPlaylistForm.get('playlistName')?.value}}</span>
                            </span>
                            <span *ngIf="isFromSchoolManagement && !selectedClassName">
                                {{addNewPlaylistForm.get('playlistName')?.value?addNewPlaylistForm.get('playlistName')?.value:'Playlist'}}
                            </span>
                        </div>
                        <div class="card-body px-0 py-0">
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <thead>
                                        <tr>
                                            <th class="p-3">Sr No</th>
                                            <th class="py-3" *ngFor="let column of columns">{{column.title}}</th>
                                            <th style="text-align: end;" class="p-3">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody cdkDropList (cdkDropListDropped)="drop($event)">
                                        <tr *ngFor="let element of playlistStories | paginate: { id: 'playlist-pagination', itemsPerPage: limit2, currentPage: offset2, totalItems: playlistStories.length }; let indexOfelement = index;"
                                            cdkDrag>
                                            <td class="wrap-text p-2">{{indexOfelement+1}} </td>
                                            <ng-container *ngFor="let column of columns; let columIndex = index;">
                                                <td class="wrap-text p-2">
                                                    {{ element[column.dataKey] }}
                                                </td>
                                            </ng-container>

                                            <td style="text-align: end;" class="p-2">
                                                <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                                    class="btn my-0 py-0" (click)="removeFromPlaylist(indexOfelement)"
                                                    ngbTooltip="Remove">
                                                    <i class="fa fa-xmark" placement="top"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group row pagination" *ngIf="playlistStories&&playlistStories.length>0">
                <div class="col-md-4 d-flex align-items-center">
                    <div class="dropdown-wrapper  position-relative">
                        <app-single-select-dropdown [options]="[
                              { label: '10', value: 10 },
                              { label: '20', value: 20 },
                              { label: '30', value: 30 },
                              { label: '40', value: 40 }
                            ]" [labelKey]="'label'" [valueKey]="'value'" [(ngModel)]="limit2"
                            (selectionChange)="pagelimit2($event)">
                        </app-single-select-dropdown>
                    </div>
                </div>

                <div class="col-md-8 pagination" style="padding-top: 14px;">
                    <pagination-controls class="custom-pagination playlist-pagination" id="playlist-pagination"
                        (pageChange)="pageChange2($event)" previousLabel="&larr;" nextLabel="&rarr;">
                    </pagination-controls>

                </div>
            </div>



            <div class="row" *ngIf="playlistStories ? playlistStories.length == 0 : !playlistStories">
                <div class="col-md-12">
                    <div class="card katha-card">
                        <div class="card-body text-center">
                            <!-- Loading state for school context when loading playlists -->
                            <div *ngIf="isFromSchoolManagement && isLoadingPlaylists" class="py-4">
                                <div class="spinner-border text-primary" aria-hidden="true"></div>
                                <p class="mt-2 mb-0">Loading existing stories for {{selectedClassName}}...</p>
                            </div>

                            <!-- Default message when not loading -->
                            <div *ngIf="!isFromSchoolManagement || !isLoadingPlaylists">
                                <p class="mb-0" *ngIf="!isFromSchoolManagement">
                                    No stories have been added to the playlist yet. You can add stories from the story
                                    list below.
                                </p>
                                <p class="mb-0"
                                    *ngIf="isFromSchoolManagement && selectedClassId && !isLoadingPlaylists">
                                    No existing stories found for {{selectedClassName}}. You can add stories from the
                                    story list below.
                                </p>
                                <p class="mb-0" *ngIf="isFromSchoolManagement && !selectedClassId">
                                    Please select a class to view existing stories or add new ones.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <div class="mt-5 position-relative">
                <app-list-with-pagination cardTitle="All Stories" [paginatioNo]="1" [perPageItems]="limit" [p]="offset"
                    [columns]="columns" [actionPermissions]="{  add : true }" [data]="storyList"
                    (showOtherModal)="addStoryInPlaylist($event)" [onCurrentPageChanged]="onCurrentPageChanged"
                    [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged"
                    [module]="_tomodule"></app-list-with-pagination>
                <div class="head-Home overTheContSerach">
                    <div class="position-relative">
                        <app-search-box (searchValue)="onSearch($event)"></app-search-box>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-start mt-5">
                <button type="button" class="btn btn-outline-secondary katha-btn mr-3"
                    [routerLink]="isFromSchoolManagement ? '/school-list' : '/playlists'">Back</button>

                <button (click)="onSubmit()" type="button" class="btn btn-outline-primary katha-btn">
                    {{isEdit ? 'Update':'Submit'}}
                </button>

            </div>

        </div>
    </div>
</app-sidebar>