import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { Constants, ModuleTypes, StoryStatus } from 'src/app/config/constants';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { isAdmin } from 'src/app/config/commonHelper';

@Component({
  selector: 'app-requests',
  templateUrl: './requests.component.html',
  styleUrls: ['./requests.component.scss']
})
export class RequestsComponent implements OnInit {
  totalNumberOfRecords: number = 0;

  offset: number = Constants.offset;
  columns: any[] = [
    { title: 'Name of the story', dataKey: 'stTitle' },
    { title: 'Genre', dataKey: 'stGenreName' },
    { title: 'Age group', dataKey: 'ageGroup' },
    { title: 'Languages', dataKey: 'sfLanguageName' },
    { title: 'Tags', dataKey: 'stTags' },
  ];
  isEdit: boolean = false;
  storyList = [];
  isAdmin= isAdmin();
  isLargeScreen: boolean = true;
  limit: number = Constants.limit;
  menuTitle: string = 'Story Management';
  searchControl: FormControl = new FormControl();
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.REQUESTS);
  _tomodule: string = ModuleTypes.REQUESTS;
  searchTerm = '';
  modalData: void;
  languageData: any;
  genresData: any;
  ageGroupData: any;
  maxNumber=Constants.maxNumber;

  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.checkScreenSize();
    this.callAllListApis();
  }

  async callAllListApis() {
    try{
    await this.getAllLanguages();
    await this.getAllAgeGroup();
    await this.getAllGenres();
    this.getStoryList();
  }catch{
    console.log("Error while fetching data");
    
  }
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getStoryList();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getStoryList() {
      const payload = {
        search: this.searchTerm,
        status:StoryStatus.DRAFT,
        limit: this.limit,
        offset: this.offset - 1,
        roleId:'',
      };
      this.ngxSpinnerService.show('globalSpinner');
      this.dataTransferService.getAllStories(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.totalNumberOfRecords = value.count;
            const storyData = value.data?.map((story: any) => {
              const sfLanguageName = Array.from(
                new Set(story?.storyFiles.map((file: any) => file.sfLanguageName))
              );
              const ageGroup = story.stAgeGroupName?.map((ageGroup:any)=>ageGroup?.sm_entityname)
              return {
                ...story,
                sfLanguageName,
                ageGroup
              };
             
            });
            this.storyList = storyData;
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getStoryList();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.getStoryList();
  };

  deleteRecord = (elementID: any) => {
    console.log('elementID', elementID);
  };

  getAllLanguages(): Promise<any> {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllLanguages(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.languageData = value.data;
            resolve(value.data);
          } else {
            resolve(null);
          }
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          reject(err);
        },
      });
    });
  }

  getAllGenres(): Promise<any> {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllGenres(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.genresData = value.data;
            resolve(value.data);
          } else {
            resolve(null);
          }
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          reject(err);
        },
      });
    });
  }

  getAllAgeGroup(): Promise<any> {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllAgeGroup(payload).subscribe({
        next: (value: any) => {
          if (value.data) {
            this.ageGroupData = value.data;
            resolve(value.data);
          } else {
            resolve(null);
          }
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.log(err);
          reject(err);
        },
      });
    });
  }
  
}
