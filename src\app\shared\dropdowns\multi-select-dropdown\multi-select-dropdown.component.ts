import { Component, Input, Output, EventEmitter, OnInit, forwardRef, ChangeDetectorRef, HostListener } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-multi-select-dropdown',
  templateUrl: './multi-select-dropdown.component.html',
  styleUrls: ['./multi-select-dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => MultiSelectDropdownComponent),
      multi: true,
    },  
  ],
})
export class MultiSelectDropdownComponent implements OnInit, ControlValueAccessor {
  @Input() options: any[] = []; 
  @Input() placeholder: string; 
  @Input() valueKey: string; 
  @Input() labelKey: string; 
  @Input() readonly: boolean = false;
  selectedItems: any[] = []; 
  filteredOptions: any[] = []; 
  searchText: string = ''; 
  showDropdown: boolean = false;

  private onChange: any = () => {};
  private onTouched: any = () => {};
  
  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    // Initialize filtered options to show all options when the component is first loaded.
    if(this.options){
      this.filteredOptions = [...this.options];
    }
    
  }

  toggleDropdown() {
    this.showDropdown = !this.showDropdown;
    
    if (this.showDropdown) {
      this.filteredOptions = [...this.options];
    }
  }

  filterOptions() {
    if (this.searchText.trim() === '') {
      this.filteredOptions = [...this.options];
    } else {
      this.filteredOptions = this.options?.filter((option) =>
        option[this.labelKey].toLowerCase().includes(this.searchText.toLowerCase())
      );
    }
  }
  
  isSelected(item: any): boolean {
    return this.selectedItems.some((selected) => selected[this.valueKey] === item[this.valueKey]);
  }

  onCheckboxChange(event: any, item: any) {
    console.log(item);
    
    if (event.target.checked) {
      this.selectedItems.push(item);
    } else {
      this.selectedItems = this.selectedItems.filter(
        (selected) => selected[this.valueKey] !== item[this.valueKey]
      );
    }
    this.propagateChange();
  }

  removeSelectedItem(item: any) {
    this.selectedItems = this.selectedItems.filter(
      (selected) => selected[this.valueKey] !== item[this.valueKey]
    );
    this.propagateChange();
  }

  propagateChange() {
    const selectedValues = this.selectedItems.map((item) => item[this.valueKey]);
    this.onChange(selectedValues);
    this.onTouched();
  }

  writeValue(value: any): void {
    if (value && Array.isArray(value)) {
      this.selectedItems = this.options?.filter((option) =>
        value.includes(option[this.valueKey])
      );
    } else {
      this.selectedItems = [];
    }
  }
  
  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    // Handle disabled state if necessary
  }

  markAsTouched() {
    if (this.onTouched) {
      this.onTouched();
    }
  }

  @HostListener('document:click', ['$event'])
    onOutsideClick(event: Event) {
      const targetElement = event.target as HTMLElement;
      if (!targetElement.closest('.custom-multiselect')) {
        this.showDropdown = false;
      }
    }
}
