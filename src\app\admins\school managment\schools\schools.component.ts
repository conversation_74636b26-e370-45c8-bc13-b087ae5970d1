import { Component, OnInit, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Constants, ModuleTypes } from 'src/app/config/constants';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
@Component({
  selector: 'app-schools',
  templateUrl: './schools.component.html',
  styleUrls: ['./schools.component.scss']
})
export class SchoolsComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent)
  listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: number = 0;
  offset: number = Constants.offset;
  columns: any[] = [
    { title: 'School Name', dataKey: 'orgTitle' },
    { title: 'Manage Playlist', dataKey: 'managePlaylist' },
    { title: 'Edit School Info', dataKey: 'editSchoolInfo' },
    { title: 'Edit School Info', dataKey: 'schoolStatus' },
  ];
  isLargeScreen: boolean = true;
  limit: number = Constants.limit;
  menuTitle: string = 'School Management';
  searchControl: FormControl = new FormControl();
  // breadCrumbModules = getBreadCrumbModules(ModuleTypes.SCHOOLLIST);
  _tomodule: string = ModuleTypes.SCHOOLLIST;
  searchTerm = '';
  schoolList: any[] = [];
  previousSearchTerm: string = '';
  activeLabel = 'Active';
  inactiveLabel = 'Inactive';
  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.getSchoolList();
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getSchoolList();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getSchoolList() {
    const payload: any = {
      limit: this.limit,
      offset: this.offset - 1,
    };

    // Add search parameter if search term exists
    if (this.searchTerm && this.searchTerm.trim()) {
      payload.orgTitle = this.searchTerm.trim();
    }

    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllSchools(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.totalNumberOfRecords = value.count;
          this.schoolList = value.data;
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getSchoolList();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.getSchoolList();
  };

  deleteRecord = (elementID: number) => {
    console.log('elementID', elementID);
  };

  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }

}
