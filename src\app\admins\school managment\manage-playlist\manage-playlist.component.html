<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div>
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div>

        <!-- Class Selection Section -->
        <div class="row mt-4" *ngIf="schoolId">
            <div class="col-lg-8">
                <div class="row">
                    <div class="col-lg-6">
                        <label class="form-label" for="class">Select Class</label>
                        <app-single-select-dropdown [options]="departmentsData" [labelKey]="'deptName'"
                            [valueKey]="'deptId'"
                            [placeholder]="isLoadingDepartments ? 'Loading classes...' : 'Select a class'"
                            [readonly]="isLoadingDepartments" [wantFullData]="true"
                            (selectionChange)="onClassSelected($event)">
                        </app-single-select-dropdown>
                    </div>
                    <div class="col-lg-6 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-primary me-2" [disabled]="!selectedClassId"
                            (click)="showCopyPlaylistModal()" title="Copy stories from another playlist">
                            <i class="fa fa-copy me-1"></i>Copy Playlist
                        </button>
                    </div>
                </div>
                <div class="mt-2" style="font-size: 12px; color: #6c757d;" *ngIf="selectedClassName">
                    <i class="fa fa-info-circle me-1"></i>
                    Managing playlist for: <strong>{{selectedClassName}}</strong>
                </div>
            </div>
        </div>

        <div class="mt-4" *ngIf="selectedClassId">
            <app-list-with-pagination cardTitle="Stories Currently Recommended" [showPagination]="false"
                [perPageItems]="5" [columns]="columns" [actionPermissions]="{  remove : true }"
                [data]="recommendedStoryData" [onDeleteRecord]="deleteRecord" [module]="_tomodule"
                (showOtherModal)="handleOpenModal($event)" modalId="recommendationModal"></app-list-with-pagination>
        </div>


        <div class="mt-5 position-relative" *ngIf="selectedClassId">
            <app-list-with-pagination cardTitle="All Stories" [perPageItems]="limit" [p]="offset" [columns]="columns"
                [actionPermissions]="{  add : true }" [data]="storyList" [onCurrentPageChanged]="onCurrentPageChanged"
                [onDeleteRecord]="deleteRecord" [totalNumberOfRecords]="totalNumberOfRecords"
                [onPageSizeChanged]="onPageSizeChanged" [module]="_tomodule" modalId="recommendationModal"
                (showOtherModal)="handleOpenModal($event)"></app-list-with-pagination>
            <div class="head-Home">
                <div class="position-relative">
                    <app-search-box (searchValue)="onSearch($event)"></app-search-box>
                </div>
            </div>
        </div>

        <!-- Message when no class is selected -->
        <div class="mt-5 text-center" *ngIf="schoolId && !selectedClassId">
            <div class="alert alert-info">
                <i class="fa fa-info-circle me-2"></i>
                Please select a class to manage its playlist.
            </div>
        </div>



        <div class="modal" id="recommendationModal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content katha-modal">
                    <div class="modal-header d-flex justify-content-end" style="border-bottom: none;">
                        <button type="button" (click)="hideModal('recommendationModal')" class="close"
                            data-bs-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body text-center my-0 py-0">
                        <p *ngIf="!isEdit" class="commonModalContent">Are you sure you want to add this story to the
                            recommended list?</p>
                        <p *ngIf="isEdit" class="commonModalContent">Are you sure you want to remove this story from the
                            recommended list?</p>
                    </div>
                    <div class="modal-footer justify-content-center d-flex" style="border-top: none;">
                        <button type="button" class="btn btn-outline-secondary"
                            (click)="hideModal('recommendationModal')">Cancel</button>
                        <button type="button" class="btn btn-outline-primary"
                            (click)="updateRecommendation()">{{isEdit?'Remove':'Add'}}</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="modal" id="languageModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content katha-modal">
                <div class="modal-header d-flex justify-content-end" style="border-bottom: none;">
                    <button type="button" (click)="hideModal('languageModal')" class="close"
                        data-bs-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body my-0 py-0">
                    <p class="commonModalContent">{{editData?.ModalHeader}}</p>
                    <p class="commonModalContent">{{editData?.ModalBody}}</p>
                </div>
                <div class="modal-footer justify-content-center" style="border-top: none;">
                    <button type="button" class="btn w-100 btn-outline-primary"
                        (click)="hideModal('languageModal')">Ok</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Copy Playlist Modal -->
    <div class="modal" id="copyPlaylistModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content katha-modal">
                <div class="modal-header d-flex justify-content-between align-items-center"
                    style="border-bottom: 1px solid #dee2e6;">
                    <h5 class="modal-title">Copy an existing playlist</h5>
                    <button type="button" (click)="hideCopyModal()" class="close"
                        data-bs-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body py-4">
                    <!-- School Selection -->
                    <div class="mb-3">
                        <label class="form-label">Select school:</label>
                        <app-single-select-dropdown [options]="organizationsData" [labelKey]="'orgTitle'"
                            [valueKey]="'orgId'"
                            [placeholder]="isLoadingOrganizations ? 'Loading schools...' : 'Select'"
                            [readonly]="isLoadingOrganizations" [wantFullData]="true"
                            (selectionChange)="onOrganizationSelected($event)">
                        </app-single-select-dropdown>
                    </div>

                    <!-- Playlist Selection -->
                    <div class="mb-3">
                        <label class="form-label">Select playlist:</label>
                        <app-single-select-dropdown [options]="availablePlaylistsData" [labelKey]="'playlistTitle'"
                            [valueKey]="'plId'"
                            [placeholder]="isLoadingPlaylists ? 'Loading playlists...' : (selectedOrgId ? 'Select' : 'Select a school first')"
                            [readonly]="isLoadingPlaylists || !selectedOrgId" [wantFullData]="true"
                            (selectionChange)="onPlaylistSelected($event)">
                        </app-single-select-dropdown>
                    </div>
                </div>
                <div class="modal-footer justify-content-center d-flex" style="border-top: 1px solid #dee2e6;">
                    <button type="button" class="btn btn-outline-secondary me-2"
                        (click)="hideCopyModal()">Cancel</button>
                    <button type="button" class="btn btn-primary" [disabled]="!selectedPlaylistId"
                        (click)="copyPlaylistStories()"
                        style="background-color: rgba(148, 193, 31, 1); border-color: rgba(148, 193, 31, 1);">
                        Submit
                    </button>
                </div>
            </div>
        </div>
    </div>
</app-sidebar>