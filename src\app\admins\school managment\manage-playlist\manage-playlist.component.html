<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div>
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div>

      

        <app-list-with-pagination cardTitle="Stories Currently Recommended" [showPagination]="false" [perPageItems]="5"
            [columns]="columns" [actionPermissions]="{  remove : true }" [data]="recommendedStoryData"
             [onDeleteRecord]="deleteRecord" [module]="_tomodule"
            (showOtherModal)="handleOpenModal($event)" modalId="recommendationModal"></app-list-with-pagination>


        <div class="mt-5 position-relative">
            <app-list-with-pagination cardTitle="All Stories" [perPageItems]="limit" [p]="offset" [columns]="columns"
                [actionPermissions]="{  add : true }" [data]="storyList" 
                [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord"
                [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged"
                [module]="_tomodule" modalId="recommendationModal"
                (showOtherModal)="handleOpenModal($event)"></app-list-with-pagination>
                <div class="head-Home">
                    <div class="position-relative">
                        <app-search-box (searchValue)="onSearch($event)"></app-search-box>
                    </div>
                </div>
        </div>

       

        <div class="modal" id="recommendationModal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content katha-modal">
                    <div class="modal-header d-flex justify-content-end" style="border-bottom: none;">
                        <button type="button" (click)="hideModal('recommendationModal')" class="close"
                            data-bs-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body text-center my-0 py-0">
                        <p *ngIf="!isEdit" class="commonModalContent">Are you sure you want to add this story to the
                            recommended list?</p>
                        <p *ngIf="isEdit" class="commonModalContent">Are you sure you want to remove this story from the
                            recommended list?</p>
                    </div>
                    <div class="modal-footer justify-content-center d-flex" style="border-top: none;">
                        <button type="button" class="btn btn-outline-secondary"
                            (click)="hideModal('recommendationModal')">Cancel</button>
                        <button type="button" class="btn btn-outline-primary"
                            (click)="updateRecommendation()">{{isEdit?'Remove':'Add'}}</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="modal" id="languageModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content katha-modal">
      <div class="modal-header d-flex justify-content-end" style="border-bottom: none;">
        <button
          type="button"
          (click)="hideModal('languageModal')"
          class="close"
          data-bs-dismiss="modal"
        >&times;</button>
      </div>
      <div class="modal-body my-0 py-0">
        <p class="commonModalContent">{{editData?.ModalHeader}}</p>
        <p class="commonModalContent">{{editData?.ModalBody}}</p>
      </div>
      <div class="modal-footer justify-content-center" style="border-top: none;">
        <button type="button" class="btn w-100 btn-outline-primary" (click)="hideModal('languageModal')">Ok</button>
      </div>
    </div>
  </div>
</div>
</app-sidebar>