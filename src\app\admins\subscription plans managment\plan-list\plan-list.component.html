<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">

        <!-- <div>
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div> -->

        <div class="row mb-4 head-Home">
            <div class="col-lg-3 position-relative">
                <!-- <app-search-box (searchValue)="onSearch($event)"></app-search-box> -->
            </div>
            <div class="col-lg-6 position-relative">
            </div>

            <div class="col-lg-3 mb-2 mb-lg-0 text-last">
                <button type="submit" class="btn btn-outline-primary"
                    (click)="showModal('addSubscriptionPlanFormModal');isEdit=false">Add New Plan</button>
            </div>
        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookClubId" [columns]="columns"
            [actionPermissions]="{  edit: true }" [data]="subscriptionPlanList"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord"
            [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged" [module]="_tomodule"
            modalId="addSubscriptionPlanFormModal"
            (showOtherModal)="handleOpenModal($event)"></app-list-with-pagination>



        <div class="modal" id="addSubscriptionPlanFormModal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content katha-modal">
                    <div class="modal-header" style="border-bottom: none;">
                        <!-- <h5 class="modal-title w-100 text-center">{{columnName=='Review' ?'Book Review':'Discussion Questions'}} -->
                        <h5 class="modal-title w-100 text-center">{{isEdit?"Edit Subscription Plan":"Add New Subscription Plan"}}</h5>
                        <button type="button" (click)="hideModal('addSubscriptionPlanFormModal')" class="close"
                            data-bs-dismiss="modal" >&times;</button>
                    </div>
                    <div class="modal-body">
                        <form class="forms-sample" (ngSubmit)="onSubmit()" [formGroup]="addSubscriptionPlanForm">
                            <div class="row">
                                <div class="col-lg-12 mb-3">
                                    <app-input-wrapper formControlName="subTitle" id="subTitle" label="Plan Name"
                                        type="text" [isRequired]="true" [isReadonly]="false"
                                        [isErrored]="addSubscriptionPlanForm.controls['subTitle']?.invalid &&addSubscriptionPlanForm.controls['subTitle']?.touched"
                                        [errorMessage]="'Plan Name is required'"
                                        ></app-input-wrapper>
                                </div>
                                <div class="col-lg-12 mb-3">
                                    <label class="required-field form-label" for="description">Description</label>
                                    <textarea required style="line-height: 1.5;" class="form-control form-text-area"
                                        formControlName="description" id="description" rows="2"></textarea>
                                        <div *ngIf="addSubscriptionPlanForm.controls['description']?.invalid && addSubscriptionPlanForm.controls['description']?.touched"
                                        class="error-msg mt-3">
                                        <div *ngIf="addSubscriptionPlanForm.controls['description'].errors?.required">Description is required.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-3">
                                    <app-input-wrapper formControlName="originalPrice" id="originalPrice"
                                        label="Regular Price" type="number" [isRequired]="true"
                                        [isReadonly]="false"
                                        [isErrored]="addSubscriptionPlanForm.controls['originalPrice']?.invalid && addSubscriptionPlanForm.controls['originalPrice']?.touched"
                                        [errorMessage]="'Regular Price is required'"
                                        ></app-input-wrapper>
                                </div>
                                <div class="col-lg-6 mb-3">
                                    <app-input-wrapper formControlName="offerPrice" id="offerPrice" label="Offer Price"
                                        type="number" [isRequired]="true" [isReadonly]="false"
                                        [isErrored]="addSubscriptionPlanForm.controls['offerPrice']?.invalid && addSubscriptionPlanForm.controls['offerPrice']?.touched"
                                        [errorMessage]="'Offer Price is required'"
                                        ></app-input-wrapper>
                                </div>
                                <div class="col-lg-6 mb-3">
                                    <app-input-wrapper formControlName="duration" id="duration" label="Duration(Days)"
                                        type="number" [isRequired]="true" [isReadonly]="false"
                                        [isErrored]="addSubscriptionPlanForm.controls['duration']?.invalid && addSubscriptionPlanForm.controls['duration']?.touched"
                                        [errorMessage]="'Duration is required'"
                                        ></app-input-wrapper>
                                </div>
                                <div class="col-lg-6 mb-3">
                                    <label for="isActive">Plan Status</label>
                                    <app-single-select-dropdown
                                    formControlName="isActive"
                                    [options]="activeOptions"
                                    [labelKey]="'label'"
                                    [valueKey]="'value'"
                                  ></app-single-select-dropdown>
                                    <div *ngIf="addSubscriptionPlanForm.controls['isActive']?.invalid && addSubscriptionPlanForm.controls['isActive']?.touched"
                                    class="error-msg mt-3">
                                    <div *ngIf="addSubscriptionPlanForm.controls['isActive'].errors?.required">Plan Status is required.
                                    </div>
                                </div>
                                </div>
                                <div class="col-lg-12 text-center mt-2">
                                    <button class="btn btn-outline-secondary mr-2 katha-btn" type="button"
                                        (click)="hideModal('addSubscriptionPlanFormModal')">Cancel</button>
                                    <button class="btn btn-outline-primary  katha-btn" type="submit">{{isEdit
                                        ?"Update":"Add"}}</button>

                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>

    </div>
</app-sidebar>