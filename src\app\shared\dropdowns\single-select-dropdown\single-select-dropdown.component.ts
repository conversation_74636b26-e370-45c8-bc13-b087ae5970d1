import { Component, EventEmitter, Input, Output, forwardRef, HostListener, OnChanges, SimpleChanges } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-single-select-dropdown',
  templateUrl: './single-select-dropdown.component.html',
  styleUrls: ['./single-select-dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SingleSelectDropdownComponent),
      multi: true,
    },
  ],
})
export class SingleSelectDropdownComponent implements ControlValueAccessor, OnChanges {
  @Input() options: Array<any> = [];
  @Input() labelKey: string;
  @Input() valueKey: string;
  @Input() placeholder: string;
  @Input() readonly: boolean = false;
  @Input() isDefaultSelected: boolean = false;
  @Output() selectionChange: EventEmitter<any> = new EventEmitter();
  @Input() wantFullData: boolean = false;
  selected: any = null;
  showDropdown: boolean = false;

  private onChange: (value: any) => void = () => { };
  private onTouched: () => void = () => { };

  ngOnChanges(changes: SimpleChanges) {
    if (changes.options && this.selected && this.options) {
      this.writeValue(this.selected[this.valueKey]);
    }
    if (changes.options) {
      this.handleDefaultSelection();
    }
  }

  toggleDropdown() {
    if (!this.readonly) {
      this.showDropdown = !this.showDropdown;
    }
  }


  onOptionSelect(option: any) {
    if (!this.readonly) {
      this.selected = option;
      if (this.wantFullData) {
        this.onChange(option);
        this.selectionChange.emit(option);
      } else {
        this.onChange(option[this.valueKey]);
        this.selectionChange.emit(option[this.valueKey]);
      }
      this.showDropdown = false;
    }
  }


  writeValue(value: any): void {
    if (this.options && value !== undefined && value !== null) {
      this.selected = this.options.find(option => option[this.valueKey] === value) || null;
    } else {
      this.handleDefaultSelection();
      this.selected = null;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  markAsTouched() {
    if (this.onTouched) {
      this.onTouched();
    }
  }

  @HostListener('document:click', ['$event'])
  onOutsideClick(event: Event) {
    const targetElement = event.target as HTMLElement;
    if (!targetElement.closest('.custom-multiselect')) {
      this.showDropdown = false;
    }
  }

  handleDefaultSelection() {
    if (this.isDefaultSelected && this.options.length > 0 && !this.selected) {
      this.onOptionSelect(this.options[0]);
    }
  }
}
