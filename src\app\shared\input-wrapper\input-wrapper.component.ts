// import { Component, forwardRef, Input, OnInit } from '@angular/core';
// import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
// @Component({
//   selector: 'app-input-wrapper',
//   templateUrl: './input-wrapper.component.html',
//   styleUrls: ['./input-wrapper.component.scss'],
//   providers: [
//     {
//       provide: NG_VALUE_ACCESSOR,
//       useExisting: forwardRef(() => InputWrapperComponent),
//       multi: true
//     }
//   ]
// })
// export class InputWrapperComponent implements OnInit {

//   @Input() id: string;
//   @Input() label: string;
//   @Input() type: string;
//   @Input() set isErrored(is) {
//     this._isErrored = is;
//   };
//   @Input() set errorMessage(value) { this._errorMessage = value }
//   @Input() isReadonly: boolean;
//   @Input() placeholder: string;
//   @Input() isRequired: boolean;
//   @Input() booleanDisplayText = false;
//   @Input() useNumberMask: boolean = false;


//   private _value: any;
//   private _errorMessage: any;
//   private _isErrored: any;
//   get errorMessage(): any {
//     return this._errorMessage;
//   }
//   get isErrored(): any {
//     return this._isErrored;
//   }
//   set value(value: any) {
//     if (this.booleanDisplayText && typeof value === 'boolean') {
//       this._value = value ? 'Yes' : 'No';
//     } else {
//       this._value = value;
//     }
//     this.onChange(value); 
//   }

//   get value(): any {
//     return this._value;
//   }

//   onChange = (event: any) => {

//   };

//   onValueChange(event: any) {
//   const newValue = event.target.value;
//   this.value = newValue; 
//   this.onTouched(); 
// }
  
//   onTouched = () => { };

//   writeValue(value: any) {
//     this.value = value;
//   }

//   registerOnChange(fn: any) {
//     this.onChange = fn;
//   }

//   registerOnTouched(fn: any) {
//     this.onTouched = fn;
//   }

//   private applyMobileMask(value: string): string {
//     return value
//       .replace(/\D/g, '')
//       .slice(0, 10); 
//   }

//   ngOnInit(): void {
//   }

  
// }



import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-input-wrapper',
  templateUrl: './input-wrapper.component.html',
  styleUrls: ['./input-wrapper.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputWrapperComponent),
      multi: true,
    },
  ],
})
export class InputWrapperComponent implements OnInit, ControlValueAccessor {
  @Input() id: string;
  @Input() label: string;
  @Input() type: string = 'text';
  @Input() set isErrored(is: any) {
    this._isErrored = is;
  }
  @Input() set errorMessage(value: string) {
    this._errorMessage = value;
  }
  @Input() isReadonly: boolean = false;
  @Input() placeholder: string = '';
  @Input() isRequired: boolean = false;
  @Input() booleanDisplayText: boolean = false;
  @Input() useNumberMask: boolean = false;
  @Input() maxLength?: number;
  private _value: any;
  private _errorMessage: string = '';
  private _isErrored: any;

  get errorMessage(): string {
    return this._errorMessage;
  }

  get isErrored(): any {
    return this._isErrored;
  }

  set value(value: any) {
    if (this.booleanDisplayText && typeof value === 'boolean') {
      this._value = value ? 'Yes' : 'No';
    } else {
      this._value = value;
    }
    this.onChange(value); // Notify Angular of the change
  }

  get value(): any {
    return this._value;
  }

  onChange = (value: any) => {};
  onTouched = () => {};

  onValueChange(event: any): void {
    let newValue = event.target.value;
  
    if (this.useNumberMask) {
      // Remove non-numeric characters and limit to 10 digits
      newValue = this.applyMobileMask(newValue);
  
      // Update the DOM value directly to prevent invalid characters
      event.target.value = newValue;
    }
  
    this.value = newValue; // Update internal value
    this.onTouched(); // Mark the control as touched
  }
  
  private applyMobileMask(value: string): string {
    return value.replace(/\D/g, '').slice(0, 10); // Remove non-numeric characters and truncate to 10 digits
  }
  

  writeValue(value: any): void {
    this._value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }


  ngOnInit(): void {}
}
