/* import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';

interface BreadcrumbModule {
  moduleId: string;
  uiLabel: string;
  target?: string;
  currentPath?: boolean;
  subModules?: BreadcrumbModule[];
}

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.css']
})
export class BreadcrumbComponent implements OnChanges {
  @Input() activeColor: string = 'text-gray-600';
  @Input() inActiveColor: string = 'text-gray-400';
  @Input() breadCrumbModules: BreadcrumbModule[] = [];
  @Input() icon: string = `
    <svg class="w-2 h-2 text-gray-400 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
    </svg>`;
  @Input() className: string = '';

  modules: BreadcrumbModule[] = [];

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.breadCrumbModules) {
      this.buildBreadcrumbs();
    }
  }

  buildBreadcrumbs(): void {
    let tempModules: BreadcrumbModule[] = [];

    const getAllParents = (modules: BreadcrumbModule[] = [], tempModules: BreadcrumbModule[]): boolean => {
      for (const module of modules) {
        if (module.currentPath) {
          tempModules.unshift(module);
          return true; // Path found, return true
        } else if (module.subModules && module.subModules.length) {
          const parentFound = getAllParents(module.subModules, tempModules);
          if (parentFound) {
            tempModules.unshift(module);
            return true; // If parent is found in submodules, return true
          }
        }
      }
      return false; // Path not found, return false
    };

    getAllParents(this.breadCrumbModules, tempModules);
    this.modules = tempModules;
  }

}
 */

import { Component, Input } from '@angular/core';

export interface BreadcrumbModule {
  label: string;
  path: string;  // The path for routing
  isActive?: boolean;  // Whether it's the current page
  state?: any;
}
@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.scss']
})

export class BreadcrumbComponent {
  @Input() breadCrumbModules: BreadcrumbModule[] = [];
  @Input() activeColor: string = 'text-active';  
  @Input() inActiveColor: string = 'text-gray-400'; 
}
