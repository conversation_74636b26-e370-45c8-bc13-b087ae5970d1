import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PlaylistService {

  constructor(private readonly http: HttpClient) { }

  addPlaylist(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/playlists/save`, payload);
  }

  updatePlaylist(payload: any) {
    return this.http.post(`${environment.kathaMehfilUrl}/playlists/update`, payload);
  }

  getPlaylistById(id: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/playlists/getById`, {
      params: {
        plId: id
      },
    });
  }
  getAllPlaylists(payload: any) {
    return this.http.get(`${environment.kathaMehfilUrl}/playlists/getAll`, {
      params: {
        limit: payload.limit,
        offset: payload.offset,
        playlistTitle: payload.search,
        playlistType: payload.playlistType,
      },
    });
  }

  getPlaylistsByEntityId(playlistEntityId: string) {
    return this.http.get(`${environment.kathaMehfilUrl}/playlists/getAll`, {
      params: {
        playlistEntityId: playlistEntityId
      },
    });
  }
}
