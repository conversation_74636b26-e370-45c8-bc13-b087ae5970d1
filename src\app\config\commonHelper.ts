import { formatDate } from '@angular/common';
import { ModuleTypes } from './constants';
export function formatDateMMMYYYY(intDatetoFormat: number): string {
  if (!intDatetoFormat) {
    return 'Unknown';
  }

  const datetoFormat = new Date(intDatetoFormat);
  return formatDate(datetoFormat, 'MMM yyyy', 'en-US');
}

export function getBreadCrumbModules(module: string, state?: any) {
  if (module) {
    switch (module) {
      case ModuleTypes.SUBSCRIPTIONPLANLIST:
        return [
          { label: 'Subscription Plans', path: '', isActive: true },
        ];
      case ModuleTypes.APPUSERS:
        return [
          { label: 'App Users List', path: '', isActive: true },
        ];
      case ModuleTypes.STORIES:
        return [
          { label: 'Story Management', path: '', isActive: true },
        ];

      case ModuleTypes.ADDSTORY:
        if (state.isFromRequestComp) {
          return [
            { label: 'Requests', path: '/stories/requests',state:state }, 
            { label: 'Edit Story', path: '', isActive: true },
          ];
        }else{
          return [
            { label: 'Story List', path: '/stories',state:state }, 
            { label: state.isEdit?'Edit Story':'Add Story', path: '', isActive: true },
          ];
        }

        case ModuleTypes.RECOMMENDATION:
        return [
          { label: 'Story List', path: '/stories' },
          { label: 'Recommendations', path: '', isActive: true },
        ];

        case ModuleTypes.REQUESTS:
          return [
            // { label: 'Stories', path: '/admins/stories' },
            { label: 'Story List', path: '/stories' },
            { label: 'Requests', path: '', isActive: true },
          ];

        case ModuleTypes.ADDEDITSCHOOL:
          return [
            { label: 'School List', path: '/school-list' },
            { label: state.isEdit?'Edit School':'Add School', path: '', isActive: true },
          ];

          case ModuleTypes.SELECTCLASS:
          return [
            { label: 'School List', path: '/school-list' },
            { label: 'Select Class', path: '', isActive: true },
          ];

          case ModuleTypes.ADDPLAYLIST:
            return [
              { label: 'Playlists', path: '/playlists' },
              { label: state.isEdit?'Edit Playlist':'Add Playlist', path: '', isActive: true },
            ];
  
      default:
        return [];
    }
  } else {
    return [];
  }
  
}

export function isAdmin(): boolean {
  const roleId = localStorage.getItem('roleId');
  return roleId == '1';
}


export function getLoggedInUserId(): any {
  return localStorage.getItem('userId');
}

export const convertSecondsToHours = (seconds: number): number => {
  if (!seconds || isNaN(seconds)) return 0;
  return Math.round(seconds / 3600);
};

export const formatSecondsToHrMin = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0 mins';

  const totalMinutes = Math.floor(seconds / 60);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  const hourStr = hours > 0 ? `${hours} hr${hours > 1 ? 's' : ''}` : '';
  const minStr = minutes > 0 ? `${minutes} min${minutes > 1 ? 's' : ''}` : '';

  return [hourStr, minStr].filter(Boolean).join(' ') || '0 mins';
};
export const enrichStoryData = (stories: any[]): any[]=> {
  return stories?.map((story: any) => {
    const sfLanguageName = Array.from(
      new Set(story?.storyFiles.map((file: any) => file.sfLanguageName))
    );

    const ageGroup = story?.stAgeGroupName?.map((age: any) => age?.sm_entityname);

    return {
      ...story,
      sfLanguageName,
      ageGroup,
    };
  }) || [];
}