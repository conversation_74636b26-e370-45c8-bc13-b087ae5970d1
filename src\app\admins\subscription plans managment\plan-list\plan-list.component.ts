import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { Constants, ModuleTypes } from 'src/app/config/constants';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { DecimalPipe } from '@angular/common';
@Component({
  selector: 'app-plan-list',
  templateUrl: './plan-list.component.html',
  styleUrls: ['./plan-list.component.scss'],
})
export class PlanListComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent)
  listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: number = 0;
  addSubscriptionPlanForm: FormGroup;
  offset: number = Constants.offset;
  columns: any[] = [
    { title: 'Plan Name', dataKey: 'subTitle' },
    // { title: 'Description', dataKey: 'description' },
    { title: 'Duration(Days)', dataKey: 'duration' },
    { title: 'Regular Price', dataKey: 'originalPrice' },
    { title: 'Offer Price', dataKey: 'offerPrice' },
    { title: 'Status', dataKey: 'isActive' },
  ];
  isEdit: boolean = false;
  subscriptionPlanList = [];

  isLargeScreen: boolean = true;
  limit: number = Constants.limit;
  menuTitle: string = 'Subscription Plans ';
  searchControl: FormControl = new FormControl();
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.SUBSCRIPTIONPLANLIST);
  _tomodule: string = ModuleTypes.SUBSCRIPTIONPLANLIST;
  searchTerm = '';
  modalData: void;
  editData: any;
  activeOptions = [
    { value: true, label: 'Active' },
    { value: false, label: 'Inactive' },
  ];
  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder,
    private decimalPipe:DecimalPipe
  ) {
    this.addSubscriptionPlanForm = this.formBuilder.group({
      subTitle: ['', Validators.required],
      description:['',Validators.required],
      duration: ['', Validators.required],
      originalPrice: ['', Validators.required],
      offerPrice: ['', Validators.required],
      isActive: [true],
      entityType: ['USER'],
    });
  }

  ngOnInit(): void {
    this.getSubscriptionPlanList();
    this.checkScreenSize();
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getSubscriptionPlanList();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getSubscriptionPlanList() {
    const decimalPipe = new DecimalPipe('en-US');
    const payload = {
      isActive: '',
      limit: this.limit,
      offset: this.offset - 1,
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllSubscriptions(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.totalNumberOfRecords = value.count;
          this.subscriptionPlanList = value.data.map((data: any) => ({
            ...data,
          originalPrice: decimalPipe.transform(data.originalPrice, '1.2-2'),
          offerPrice: decimalPipe.transform(data.offerPrice, '1.2-2'),
        }));
      }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getSubscriptionPlanList();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    this.getSubscriptionPlanList();
  };

  deleteRecord = (elementID: any) => {
    console.log('elementID', elementID);
  };

  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }

  showModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }
    this.addSubscriptionPlanForm.reset();
  }

  handleOpenModal(event: { modalId: string; isEdit: boolean; data?: any }) {
    this.isEdit = event.isEdit;
    this.addSubscriptionPlanForm.patchValue(event.data);
    this.editData = event.data;
    this.showModal(event.modalId);
  }

  onSubmit() {
    this.addSubscriptionPlanForm.markAllAsTouched();
    if(this.addSubscriptionPlanForm.valid){
      this.ngxSpinnerService.show('globalSpinner');
    const formData = this.isEdit
      ? {
          subId: this.editData.subId,
          subTitle: this.addSubscriptionPlanForm.get('subTitle')?.value,
          description: this.addSubscriptionPlanForm.get('description')?.value,
          offerPrice: Number(this.addSubscriptionPlanForm.get('offerPrice')?.value),
          originalPrice: Number(this.addSubscriptionPlanForm.get('originalPrice')?.value),
          duration: Number(this.addSubscriptionPlanForm.get('duration')?.value),
          isActive: this.addSubscriptionPlanForm.get('isActive')?.value,
        }
      : {
          ...this.addSubscriptionPlanForm.value,
          offerPrice: Number(this.addSubscriptionPlanForm.get('offerPrice')?.value),
          originalPrice: Number(this.addSubscriptionPlanForm.get('originalPrice')?.value),
          duration: Number(this.addSubscriptionPlanForm.get('duration')?.value),
        };
  
    const apiCall = this.isEdit
      ? this.dataTransferService.updateSubscriptionPlan(formData)
      : this.dataTransferService.addNewSubscriptionPlan(formData);
  
    apiCall.subscribe({
      next: (response: any) => {
        this.toastr.success(response.message);
        this.getSubscriptionPlanList();
        this.hideModal('addSubscriptionPlanFormModal');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.error('Error creating or updating subscription:', err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
        this.hideModal('addSubscriptionPlanFormModal');
      },
    });
  }
}
  

}
