<label [for]="id" [ngClass]="{ 'form-label': true, 'required-field': isRequired }">
    {{ label }}
  </label>
  <input
    [ngClass]="{ readonly: isReadonly }"
    [type]="type"
    [placeholder]="placeholder ? placeholder : ''"
    class="form-control"
    [value]="value ? value : ''"
    (blur)="onTouched()"
    (input)="onValueChange($event)"
    [readonly]="isReadonly"
    [attr.maxlength]="maxLength || null"
  />
  <div class="error-msg mt-3" *ngIf="isErrored">
    {{ errorMessage }}
  </div>
  