.katha-card {
  .table {
    table-layout: fixed; // Crucial for fixed column widths

    td {
      word-wrap: break-word; // To handle long content within fixed width cells

      // Ensures that the custom components fill the width of the table cell
      app-input-wrapper,
      app-multi-select-dropdown {
        width: 100%;
        display: block;
      }
    }

    // Define fixed widths for columns.
    // These apply to both header (th) and data (td) cells.
    // Column order: User Name | Email | Phone Number | Action (removed Class column)
    thead tr th,
    tbody tr td {
      &:nth-child(1) {
        // User Name column
        width: 200px; // Increased width since we removed class column
      }

      &:nth-child(2) {
        // Email column
        width: 250px; // Increased width
      }

      &:nth-child(3) {
        // Phone Number column
        width: 200px; // Increased width
      }

      &:nth-child(4) {
        // Action column
        width: 120px; // Increased width for action buttons
        text-align: center; // Keep action buttons centered
      }
    }
  }

  // Class sub-row styling - seamless integration with main table rows
  .class-sub-row {
    background: transparent !important;
    border: none !important;

    .class-sub-row-cell {
      border: none !important;
      padding: 2px 8px 8px 8px !important; // Minimal top padding, consistent side padding
      background: transparent !important;
    }
  }

  // Class display container for read-only view - seamless integration
  .class-display-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0;
    background: transparent;

    .class-label {
      color: #F8FAF9;
      font-size: 12px;
      font-weight: 500;
      min-width: 50px;
    }

    .class-values {
      color: #94C11F;
      font-size: 12px;
      font-weight: 400;
    }
  }

  // Class selection container for edit mode - seamless integration
  .class-selection-container {
    .class-label {
      color: #F8FAF9;
      font-size: 12px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    // Checkbox grid layout - integrated styling
    .class-checkbox-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
      gap: 6px;
      max-height: 140px;
      overflow-y: auto;
      padding: 4px 0;
      background: transparent;
      border: none;
      border-radius: 0;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #2D2D2D;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #94C11F;
        border-radius: 3px;
      }
    }

    .class-checkbox-item {
      .form-check {
        display: flex;
        align-items: center;
        padding: 2px 4px;
        background: transparent;
        border-radius: 4px;
        border: none;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;

        &:hover {
          background: rgba(148, 193, 31, 0.1);
        }

        // Hide default checkbox
        .form-check-input {
          position: absolute;
          opacity: 0;
          cursor: pointer;
          height: 0;
          width: 0;
        }

        // Custom checkbox styling using input-helper
        .input-helper {
          position: relative;
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 8px;
          flex-shrink: 0;

          &:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 16px;
            height: 16px;
            border: 2px solid #94C11F;
            border-radius: 3px;
            background: transparent;
            transition: all 0.3s ease;
          }

          &:after {
            content: '\2713';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            font-size: 12px;
            font-weight: bold;
            color: #1E1E1E;
            opacity: 0;
            transition: all 0.3s ease;
          }
        }

        // Checked state
        .form-check-input:checked+.input-helper {
          &:before {
            background: #94C11F;
            border-color: #94C11F;
          }

          &:after {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
        }

        .form-check-label {
          color: #F8FAF9;
          font-size: 12px;
          font-weight: 400;
          cursor: pointer;
          user-select: none;
          line-height: 1.3;
        }
      }
    }

    .no-classes-message {
      color: #aab2bd;
      font-size: 11px;
      text-align: left;
      padding: 4px 0;
      background: transparent;
      border: none;
      border-radius: 0;
    }

    .error-msg {
      color: #FF4747;
      font-size: 11px;
      margin-top: 4px;
      padding: 4px 8px;
      background: rgba(255, 71, 71, 0.1);
      border-radius: 4px;
      border: 1px solid rgba(255, 71, 71, 0.3);
    }
  }
}

// Disabled button styling for row creation restrictions
.btn-disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;

  &:hover {
    opacity: 0.6 !important;
    transform: none !important;
  }
}