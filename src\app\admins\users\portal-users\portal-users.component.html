<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">

        <!-- <div>
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div> -->

        <div class="row mb-4 head-Home">
            <div class="col-lg-3 position-relative">
                <app-search-box (searchValue)="onSearch($event)"></app-search-box>
            </div>
            <div class="col-lg-6 position-relative">
            </div>
            <div class="col-lg-3 mb-2 mb-lg-0 text-last mt-small-2">
                <button type="button" class="btn btn-outline-primary" (click)="showModal('addNewUserModal')">Add New
                    User</button>
            </div>
        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" [columns]="columns"
            [actionPermissions]="false" [data]="portalUsers"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord"
            [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged" [module]="_tomodule"
            modalId="addNewUserModal" (showOtherModal)="handleOpenModal($event)"></app-list-with-pagination>



        <div class="modal" id="addNewUserModal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content katha-modal">
                    <div class="modal-header" style="border-bottom: none;">
                        <!-- <h5 class="modal-title w-100 text-center">{{columnName=='Review' ?'Book Review':'Discussion Questions'}} -->
                        <h5 class="modal-title w-100 text-center">{{isEdit?"Edit Portal User":"Add New Portal User"}}</h5>
                        <button type="button" (click)="hideModal('addNewUserModal')" class="close"
                            data-bs-dismiss="modal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form class="forms-sample" (ngSubmit)="onSubmit()" [formGroup]="addNewUserForm">
                            <div class="row">
                                <div class="col-lg-12 mb-3">
                                    <app-input-wrapper formControlName="name" id="name" label="Full Name" type="text"
                                    [isRequired]="true" [isReadonly]="false"
                                    [isErrored]="addNewUserForm.controls['name']?.invalid && (addNewUserForm.controls['name']?.touched)"
                                    [errorMessage]="getErrorMsg('name','Name')"></app-input-wrapper>
                                </div>
                                <div class="col-lg-12 mb-3">
                                    <app-input-wrapper formControlName="userEmailId" id="userEmailId" label="Email" type="text"
                                    [isRequired]="true"
                                    [isReadonly]="false"
                                    [isErrored]="addNewUserForm.controls['userEmailId']?.invalid && (addNewUserForm.controls['userEmailId']?.touched)"
                                    [errorMessage]="getErrorMsg('userEmailId','Email')"
                                    ></app-input-wrapper>
                                </div>
                                <div class="col-lg-12 mb-3">
                                    <app-input-wrapper 
                                      formControlName="userPhoneNumber" 
                                      id="userPhoneNumber" 
                                      label="Mobile Number" 
                                      type="text"
                                      [isRequired]="true"
                                      [isReadonly]="false"
                                      [useNumberMask]="true"
                                      [isErrored]="addNewUserForm.controls['userPhoneNumber']?.invalid && addNewUserForm.controls['userPhoneNumber']?.touched"
                                      [errorMessage]="getErrorMsg('userPhoneNumber', 'Mobile Number')">
                                    </app-input-wrapper>
                                  </div>
                                  <div class="col-lg-12 mb-3">
                                    <label class="form-label required-field" for="userRoleId">Role</label>
                                    <app-single-select-dropdown formControlName="userRoleId" [options]="rolesData"
                                        [labelKey]="'name'" [valueKey]="'id'"></app-single-select-dropdown>
                                    <div *ngIf="addNewUserForm.get('userRoleId')?.invalid && addNewUserForm.get('userRoleId')?.touched"
                                        class="error-msg mt-3">
                                        <div *ngIf="addNewUserForm.get('userRoleId')?.errors?.required">Role is required.</div>
                                    </div>
                                </div>
                                <div class="col-lg-12 text-center mt-3">
                                    <button class="btn btn-outline-secondary mr-2 katha-btn" type="button"
                                        (click)="hideModal('addNewUserModal')">Cancel</button>
                                    <button class="btn btn-outline-primary katha-btn" type="submit">{{isEdit?"Update":"Add"}}</button>

                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>
</app-sidebar>