Stack trace:
Frame         Function      Args
0007FFFFAA60  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFAA60, 0007FFFF9960) msys-2.0.dll+0x2118E
0007FFFFAA60  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFAA60  0002100469F2 (00021028DF99, 0007FFFFA918, 0007FFFFAA60, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAA60  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAA60  00021006A545 (0007FFFFAA70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFAA70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA2F2D0000 ntdll.dll
7FFA2E750000 KERNEL32.DLL
7FFA2CC40000 KERNELBASE.dll
7FFA2E540000 USER32.dll
7FFA2C730000 win32u.dll
7FFA2EDB0000 GDI32.dll
7FFA2C610000 gdi32full.dll
7FFA2CAE0000 msvcp_win.dll
7FFA2C760000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA2E1E0000 advapi32.dll
7FFA2DF00000 msvcrt.dll
7FFA2E130000 sechost.dll
7FFA2C5E0000 bcrypt.dll
7FFA2E2B0000 RPCRT4.dll
7FFA2B620000 CRYPTBASE.DLL
7FFA2C8F0000 bcryptPrimitives.dll
7FFA2D1D0000 IMM32.DLL
