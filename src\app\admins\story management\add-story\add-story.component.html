<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div class="mt-0">
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div>


        <div class="add-new-story">
            <span class="heading-text">{{isEdit?'Edit Story':'Add New Story'}}</span>
            <form class="forms-sample mt-5" [formGroup]="addNewStoryForm">
                <div class="row mb-5 katha-card py-3 mx-0 px-0">
                    <div class="col-lg-6 mb-5">
                        <app-input-wrapper formControlName="stTitle" id="stTitle" label="Title of the story" type="text"
                            [isRequired]="true" [isReadonly]="(isStoryReadOnly&&!isAdmin)||isFromRequestComp"
                            [isErrored]="addNewStoryForm.controls['stTitle']?.invalid && (addNewStoryForm.controls['stTitle']?.touched)"
                            [errorMessage]="'Title is required.'"></app-input-wrapper>

                    </div>
                    <div class="col-lg-6 mb-5">
                        <app-input-wrapper formControlName="stTags" id="stTags" label="Tags (to improve searchability)"
                            type="text" [isRequired]="true"
                            [isReadonly]="(isStoryReadOnly&&!isAdmin)||isFromRequestComp"
                            [isErrored]="addNewStoryForm.controls['stTags']?.invalid && addNewStoryForm.controls['stTags']?.touched"
                            [errorMessage]="'Tags is required.'"></app-input-wrapper>
                    </div>
                    <div class="col-lg-12 mb-5">
                        <label class="form-label" for="stDescription">Description</label>
                        <textarea style="line-height: 1.5;" class="form-control form-text-area"
                            formControlName="stDescription" id="stDescription" rows="3"
                            [readOnly]="(isStoryReadOnly&&!isAdmin)||isFromRequestComp"></textarea>
                        <div *ngIf="addNewStoryForm.controls['stDescription']?.invalid && addNewStoryForm.controls['stDescription']?.touched"
                            class="error-msg mt-3">
                            <!-- <div *ngIf="addNewStoryForm.controls['stDescription'].errors?.required">Description is
                                required.</div> -->
                        </div>
                    </div>
                    <div class="col-lg-6 mb-5">
                        <label class="form-label required-field" for="ageGroupId">Age group</label>
                        <!-- <app-single-select-dropdown [readonly]="(isStoryReadOnly&&!isAdmin)||isFromRequestComp" formControlName="ageGroupId"
                            [options]="ageGroupData" [labelKey]="'title'"
                            [valueKey]="'agId'"></app-single-select-dropdown> -->
                        <app-multi-select-dropdown [readonly]="(isStoryReadOnly&&!isAdmin)||isFromRequestComp"
                            [options]="ageGroupData" [valueKey]="'agId'" [labelKey]="'title'"
                            formControlName="ageGroupId">
                        </app-multi-select-dropdown>
                        <div *ngIf="addNewStoryForm.controls['ageGroupId']?.invalid && addNewStoryForm.controls['ageGroupId']?.touched"
                            class="error-msg mt-3">
                            <div *ngIf="addNewStoryForm.controls['ageGroupId'].errors?.required">Age group is required.
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 mb-5">
                        <label class="form-label required-field" for="stGenreId">Genre</label>
                        <!-- <app-multi-select-dropdown [options]="genresData" [valueKey]="'genId'" [labelKey]="'title'"
                            formControlName="stGenreId">
                        </app-multi-select-dropdown> -->
                        <app-single-select-dropdown 
                        [readonly]="(isStoryReadOnly && !isAdmin) || isFromRequestComp"
                        formControlName="stGenreId" 
                        [options]="genresData" 
                        [labelKey]="'title'"
                        [valueKey]="'genId'"
                        [wantFullData]="true"
                        (selectionChange)="onGenreSelected($event)">
                      </app-single-select-dropdown>
                      

                        <div *ngIf="addNewStoryForm.controls['stGenreId']?.invalid && addNewStoryForm.controls['stGenreId']?.touched"
                            class="error-msg mt-3">
                            <div *ngIf="addNewStoryForm.controls['stGenreId'].errors?.required">Genre is required.</div>
                        </div>
                    </div>

                    <div *ngIf="!isFromRequestComp" class="col-lg-12 text-start">
                        <button (click)="onMetaDataSubmit()"
                            [disabled]="(isStoryReadOnly&&!isAdmin) || (!isEdit&&storyId)" type="button"
                            class="btn btn-outline-primary katha-btn">{{isEdit ? 'Update'
                            :'Submit'}}</button>
                    </div>

                </div>
            </form>



            <form *ngIf="storyId" class="forms-sample mt-5" [formGroup]="storyFileForm">
                <div formArrayName="storyFiles">
                    <div class="row mb-5 py-3 katha-card mx-0 px-0"
                        *ngFor="let story of storyFileArray.controls; let i = index" [formGroupName]="i">
                        <div class="col-lg-3">
                            <app-input-wrapper formControlName="sfStoryTellerName" id="sfStoryTellerName"
                                label="Storyteller Name" type="text" [isRequired]="true" [isReadonly]="true"
                                [isErrored]="story.get('sfStoryTellerName')?.invalid && story.get('sfStoryTellerName')?.touched"
                                [errorMessage]="'Storyteller name is required.'">
                            </app-input-wrapper>
                        </div>
                        <div class="col-lg-3 mt-small-2">
                            <app-input-wrapper formControlName="sfAuthorName" id="sfAuthorName" label="Author Name"
                                type="text" [isRequired]="false"
                                [isReadonly]="(!isRowEditing(i)&&isStoryFileExists(i))||!isStoryFileCreatedByLoggedInUser(story.get('sfStoryTellerId')?.value)||isStoryRejected(i)"
                                [isErrored]="story.get('sfAuthorName')?.invalid && story.get('sfAuthorName')?.touched"
                                [errorMessage]="'Author is required.'">
                            </app-input-wrapper>

                        </div>
                        <div class="col-lg-3 mt-small-2">
                            <label class="form-label required-field" for="sfLanguageId">Language(s)</label>
                            <!-- <select required formControlName="sfLanguageId" id="sfLanguageId" class="form-control">
                                <option selected disabled [value]="null">Select</option>
                                <option *ngFor="let lang of languageData" value="{{lang.laId}}">{{lang.laTitle}}
                                </option>
                            </select> -->
                            <app-single-select-dropdown formControlName="sfLanguageId" [options]="languageData"
                                [labelKey]="'laTitle'"
                                [readonly]="(!isRowEditing(i)&&isStoryFileExists(i))||!isStoryFileCreatedByLoggedInUser(story.get('sfStoryTellerId')?.value)||isStoryRejected(i)"
                                [valueKey]="'laId'"></app-single-select-dropdown>
                            <div *ngIf="story.get('sfLanguageId')?.invalid && story.get('sfLanguageId')?.touched"
                                class="error-msg mt-3">
                                <div *ngIf="story.get('sfLanguageId')?.errors?.required">Language is required.</div>
                            </div>
                        </div>
                        <div class="col-md-3 mt-small-2">
                            <label for="sfFileName-{{i}}" class="form-label required-field">Upload</label>
                            <label for="sfFileName-{{i}}" class="form-control m-0 fileUploader"
                                [ngClass]="{'readonly': story.get('sfStatus')?.value === 'PUBLISHED' ||!isStoryFileCreatedByLoggedInUser(story.get('sfStoryTellerId')?.value)||(!isRowEditing(i)&&isStoryFileExists(i))||isStoryRejected(i)}">
                                <span class="fileName">{{ story.get('sfFileName')?.value || 'Audio file formats(MP3,WAV)' }}</span>
                                <img *ngIf="(!isRowEditing(i)&&isStoryFileExists(i))||story.get('sfStatus')?.value!=='PUBLISHED' ||!isStoryFileCreatedByLoggedInUser(story.get('sfStoryTellerId')?.value)||isStoryRejected(i)"
                                    class="fileUploadIcon" src="./assets/icons/Uploaded-Stories.svg" alt="Upload Icon">
                            </label>
                            <input
                                [disabled]="(!isRowEditing(i)&&isStoryFileExists(i))||story.get('sfStatus')?.value=='PUBLISHED'||!isStoryFileCreatedByLoggedInUser(story.get('sfStoryTellerId')?.value)||isStoryRejected(i)"
                                id="sfFileName-{{i}}" type="file" accept=".mp3, .wav" style="display: none;"
                                (change)="onFileChange($event, i)">
                            <div *ngIf="story.get('sfFileName')?.errors?.invalidFormat" class="error-msg mt-3">
                                Please upload the audio file in the correct format (MP3, WAV).
                            </div>
                            <div *ngIf="story.get('sfFileName')?.errors?.required && story.get('sfFileName')?.touched"
                                class="error-msg mt-3">
                                File is required.
                            </div>
                        </div>

                        <div *ngIf="story.get('audioSrc')?.value"
                            class="col-md-12 mt-4 audioGroup d-flex justify-content-between">
                            <div class="m-0 p-0">
                                <audio [src]="story.get('audioSrc')?.value" class="custom-audio-player mr-2"
                                    controls></audio>
                            </div>

                            <div class="m-0 p-0 d-flex">
                                <div *ngIf="(isStoryFileCreatedByLoggedInUser(story.get('sfStoryTellerId')?.value)&&!isFromRequestComp)&&!isStoryRejected(i)"
                                    class="m-0 p-0">
                                    <button *ngIf="!isRowEditing(i)&&isStoryFileExists(i)" type="button"
                                        (click)="toggleEditMode(i)"
                                        class="btn btn-outline-secondary katha-btn mr-2 mt-small-2">
                                        Edit
                                    </button>

                                    <button *ngIf="isRowEditing(i)||!isStoryFileExists(i)" type="button"
                                        (click)="onStoryFileSubmit(i)"
                                        class="btn btn-outline-primary katha-btn mr-2 mt-small-2">
                                        Save
                                    </button>
                                    <button *ngIf="isRowEditing(i)&&isStoryFileExists(i)" type="button"
                                        (click)="toggleEditMode(i)"
                                        class="btn btn-outline-secondary katha-btn mr-4 mt-small-2">
                                        Cancel
                                    </button>
                                </div>

                                <div *ngIf="isStoryFileExists(i)&&story.get('sfStatus')?.value !== 'PUBLISHED'"
                                    class="m-0 p-0 d-flex">
                                    <button
                                        *ngIf="isAdmin && (story.get('sfStatus')?.value !== 'PUBLISHED' && story.get('sfStatus')?.value == 'DRAFT'||!isStoryRejected(i))"
                                        type="button" (click)="publishFile(i,'PUBLISHED')"
                                        class="btn btn-outline-primary katha-btn mr-2 mt-small-2">
                                        Publish
                                    </button>
                                    <button
                                        *ngIf="isAdmin && (story.get('sfStatus')?.value !== 'PUBLISHED' && story.get('sfStatus')?.value == 'DRAFT'||!isStoryRejected(i))"
                                        type="button" (click)="publishFile(i,'REJECTED')"
                                        class="btn btn-outline-danger katha-btn mt-small-2">
                                        Reject
                                    </button>
                                </div>

                            </div>
                        </div>


                    </div>


                    <div *ngIf="!isFromRequestComp" class="col-lg-12 mx-0 px-0 text-start">
                        <button type="button" class="btn btn-outline-primary katha-btn" (click)="createStoryRow()">Add a
                            new language</button>
                    </div>
                </div>
            </form>

            <div class="d-flex justify-content-start mt-5">
                <button type="button" class="btn btn-outline-secondary katha-btn mr-3"
                    (click)="onBackClick();">Back</button>
            </div>

        </div>
    </div>
</app-sidebar>