<div class="container-scroller">
  <div class="container-fluid page-body-wrapper full-page-wrapper">
    <div class="content-wrapper d-flex align-items-center auth px-0 justify-content-center">
      <div class="row login-form">
        <div class="col-lg-12  p-4">
            <div class="d-flex justify-content-center align-items-center">
              <img height="72px" src="./assets/images/katha-mehfil-main-logo.svg" />
            </div>
  
          <form  class="mt-5" *ngIf="!isOtpSent" [formGroup]="loginForm" (ngSubmit)="onSendOtp()">
            <div class="form-group">
              <label for="userEmailId">Email</label>
              <input type="email" id="userEmailId" class="form-control form-control-lg" formControlName="userEmailId"
                placeholder="Enter your email address" />
              <div *ngIf="loginForm.controls['userEmailId'].invalid && loginForm.controls['userEmailId'].touched"
                class="error-msg mt-3">
                <div *ngIf="loginForm.controls['userEmailId'].errors?.required">Email is required.</div>
                <div *ngIf="loginForm.controls['userEmailId'].errors?.pattern">Enter a valid email address.</div>
              </div>
            </div>
            <button type="submit mt-2" class="btn btn-block btn-outline-primary">Send OTP</button>
          </form>

          <form class="mt-5" *ngIf="isOtpSent" [formGroup]="otpForm" (ngSubmit)="onSubmitOtp()">
            <div class="form-group text-center">
              <label for="otp">Enter OTP</label>
              <div class="d-flex justify-content-center otp-inputs">
                <input #otp1Input type="text" maxlength="1" class="form-control otp-box"
                formControlName="otp1" (keyup)="onKeyUp($event, 1)" (paste)="onPaste($event)" />         
                <input type="text" maxlength="1" class="form-control otp-box" formControlName="otp2"
                  (keyup)="onKeyUp($event, 2)" />
                <input type="text" maxlength="1" class="form-control otp-box" formControlName="otp3"
                  (keyup)="onKeyUp($event, 3)" />
                <input type="text" maxlength="1" class="form-control otp-box" formControlName="otp4"
                  (keyup)="onKeyUp($event, 4)" />
                <input type="text" maxlength="1" class="form-control otp-box" formControlName="otp5"
                  (keyup)="onKeyUp($event, 5)" />
                <input type="text" maxlength="1" class="form-control otp-box" formControlName="otp6"
                  (keyup)="onKeyUp($event, 6)" />
              </div>
              <div class="d-flex justify-content-between mt-3">
                <div class="text-start">
                <p *ngIf="invalidOtp" class="error-msg">Invalid OTP. Please request a new one.</p>
                </div>
                <div class="text-last">
                <p class="resend-options" *ngIf="!canResend">Resend OTP in: {{ formattedTimer }}</p>
                <a style="text-decoration-line: underline;text-decoration-style: solid;text-underline-position: from-font;text-decoration-skip-ink: auto;
                " *ngIf="canResend" type="button" class="resend-options" (click)="onResendOtp()">
                  Resend OTP?
                </a>
                </div>
              </div>
            </div>

           


            <div class="d-flex justify-content-center flex-column mt-3">
              <button [ngClass]="{'disable-btn': this.otpForm.invalid}" [disabled]="this.otpForm.invalid" type="submit"
                class="btn btn-outline-primary  katha-btn mr-2">
                Login
              </button>
              <button type="button" class="btn btn-outline-secondary katha-btn mt-3" (click)="onCancelOtp()">Go back</button>
            </div>
          </form>
        </div>


      </div>
    </div>
  </div>
</div>

<div class="modal" id="commonModal">
  <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content katha-modal">
          <div class="modal-header d-flex justify-content-end" style="border-bottom: none;">
              <button type="button" (click)="hideModal('commonModal')" class="close" data-bs-dismiss="modal"
                  >&times;</button>
          </div>
          <div class="modal-body my-0 py-0">
              <p class="commonModalContent">A new OTP has been sent to your email address</p>
            </div>
          <div class="modal-footer justify-content-center" style="border-top: none;">
              <button type="button" class="btn w-100 btn-outline-primary"
                  (click)="hideModal('commonModal')">Ok</button>
          </div>
      </div>
  </div>
</div>