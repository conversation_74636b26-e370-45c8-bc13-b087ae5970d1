import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { ModuleTypes } from 'src/app/config/constants';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
@Component({
  selector: 'app-categories',
  templateUrl: './categories.component.html',
  styleUrls: ['./categories.component.scss']
})
export class CategoriesComponent implements OnInit {
listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: number = 0;
  offset: number = 1;
  columns: any[] = [
    { title: 'Name of the story', dataKey: 'stTitle' },
    { title: 'Author Name', dataKey: 'stCreatedBy' },
    { title: 'Genre', dataKey: 'stGenre' },
    { title: 'Age group', dataKey: 'stAgeGroup' },
    { title: 'Languages', dataKey: 'sfLanguageId' },
    { title: 'Tags', dataKey: 'stTags' },
  ];
  isEdit: boolean = false;
  storyList = [];

  isLargeScreen: boolean = true;
  limit: number = 10;
  menuTitle: string = 'Stories';
  searchControl: FormControl = new FormControl();
  breadCrumbModules = getBreadCrumbModules(ModuleTypes.STORIES);
  _tomodule: string = ModuleTypes.STORIES;
  searchTerm = '';
  modalData: void;

  constructor(
     private toastr: ToastrService,
        private ngxSpinnerService: NgxSpinnerService,
        private dataTransferService: DataTransferService,
        private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
  }

  
  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    // this.getStoryList();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset = 1;
    this.limit = pageSizeChanged;
    // this.getStoryList();
  };

  deleteRecord = (elementID: number) => {
    console.log('elementID', elementID);
  };

  getStoryList() {
    const payload = {
      limit: this.limit,
      offset: this.offset - 1,
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllStories(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.totalNumberOfRecords = value.count;
          const storyData = value.data.map((story:any)=>{
            const LanguageId=story?.storyFiles.map((file:any)=>file.sfLanguageId);
            const Genre=story?.stGenreId.title;
            const AgeGroup=story?.stAgeGroupId.title;

            return {
              ...story,
              sfLanguageId:LanguageId,
              stGenre:Genre,
              stAgeGroup:AgeGroup
            }
          });
          this.storyList=storyData;
          console.log("storyList",this.storyList);
          
      }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }
  
  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    this.getStoryList();
  }

}
