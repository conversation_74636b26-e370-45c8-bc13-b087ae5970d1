import { Directive, Input, ElementRef, Renderer2, OnInit } from '@angular/core';

@Directive({
  selector: '[isSameRole]'
})
export class IsSameRoleDirective implements OnInit {

  @Input('isSameRole') targetId!: string; 

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    const userId = localStorage.getItem('userId'); 

    if (userId && this.targetId === userId) {
      this.renderer.removeClass(this.el.nativeElement, 'readonly');
    } else {
      this.renderer.addClass(this.el.nativeElement, 'readonly');
    }
  }
}
