import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormArray,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { getBreadCrumbModules, getLoggedInUserId } from 'src/app/config/commonHelper';
import { ModuleTypes, StoryStatus, UserTypes } from 'src/app/config/constants';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { Constants } from 'src/app/config/constants';
import { isAdmin } from 'src/app/config/commonHelper';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Router } from '@angular/router';


@Component({
  selector: 'app-add-story',
  templateUrl: './add-story.component.html',
  styleUrls: ['./add-story.component.scss'],
})
export class AddStoryComponent implements OnInit, OnDestroy {
  totalNumberOfRecords: number = 0;
  addNewStoryForm: FormGroup;
  storyFileForm: FormGroup;
  menuTitle: string = 'Story Management';
  _tomodule: string = ModuleTypes.ADDSTORY;
  isStoryCreatedByLoggedInUser: boolean = false;
  loggedInUserId: any = getLoggedInUserId();
  isEdit: boolean = false;
  params: any;
  selectedFile: File | null = null;
  audioSrc: string | null = null;
  audioPlayers: { [index: number]: HTMLAudioElement } = {};
  isPlaying: boolean = false;
  isFromRequestComp: boolean = false;
  selectedStoryId: any;
  metaData: any;
  languageData: any;
  genresData: any;
  ageGroupData: any;
  isStoryPublished: false;
  authorData = []
  storytellerData = []
  storyId: any;
  roleId: any;
  s3BucketUrl = Constants.s3BaseUrl;
  isAdmin = false;
  isEditing: boolean = false;
  breadCrumbModules: ({ label: string; path: string; isActive?: undefined; } | { label: string; path: string; isActive: boolean; })[];
  searchTerm: string;
  maxNumber = Constants.maxNumber;
  selectedGenre: any='';

  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private dataTransferService: DataTransferService,
    private formBuilder: FormBuilder,
    private cdr: ChangeDetectorRef,
    private sanitizer: DomSanitizer,
    private router: Router,
  ) {

    this.isAdmin = isAdmin();
    console.log("isADmin",this.isAdmin);
    


    this.addNewStoryForm = this.formBuilder.group({
      stTitle: ['', Validators.required],
      stDescription: [''],
      stGenreId: ['', Validators.required],
      ageGroupId: ['', Validators.required],
      stTags: ['', Validators.required],
      stStatus: [StoryStatus.DRAFT],
      stDp: ['']
    });

    this.storyFileForm = this.formBuilder.group({
      storyFiles: this.formBuilder.array([this.addNewStoryFile()]),
    });
  }


  ngOnDestroy(): void {
    localStorage.removeItem('selectedStoryId');
    localStorage.removeItem('isFromRequestComp');
    this.pauseAll();
    this.audioPlayers = {};
  }

  ngOnInit(): void {
    this.getAllLanguages();
    this.getAllAgeGroup();
    this.getAllGenres();
    this.getAllAuthors();
    this.params = history?.state;

    if (this.params.isFromRequestComp) {
      this.isFromRequestComp = this.params.isFromRequestComp;
      this.selectedStoryId = this.params.element.stId;
      localStorage.setItem('isFromRequestComp', 'true')
      // this.storyId=this.selectedStoryId;
    }

    const RequestCompCache = localStorage.getItem('isFromRequestComp');
    if (RequestCompCache === 'true') {
      this.isFromRequestComp = true;
    } else {
      this.isFromRequestComp = false;
    }

    if (this.params?.isEdit) {
      this.isEdit = this.params?.isEdit;
      this.selectedStoryId = this.params?.data?.stId;
      this.searchTerm = this.params?.searchTerm;
    }

    if (this.selectedStoryId) {
      localStorage.setItem('selectedStoryId', this.selectedStoryId);
    }
    this.storyId = this.selectedStoryId ? this.selectedStoryId : localStorage.getItem('selectedStoryId');

    if (this.storyId) {
      this.isEdit = true;
      this.getStoryById(this.storyId);
    } else {
      this.isEdit = false;
    }

    this.breadCrumbModules = getBreadCrumbModules(ModuleTypes.ADDSTORY, { isEdit: this.isEdit, searchTerm: this.searchTerm, isFromRequestComp: this.isFromRequestComp });

  }

  get storyFileArray(): FormArray {
    return this.storyFileForm.get('storyFiles') as FormArray;
  }

  get isStoryReadOnly(): boolean {
    return this.isEdit && !this.isStoryCreatedByLoggedInUser;
  }

  isStoryFileCreatedByLoggedInUser(sfStoryTellerId: any) {
    return this.loggedInUserId === sfStoryTellerId;
  }

  isRowEditing(index: number): boolean {
    return this.storyFileArray.at(index).get('isEditing')?.value;
  }

  isStoryFileExists(index: number) {
    return this.storyFileArray.at(index).get('sfId')?.value;
  }

  isStoryRejected(index: number) {
    return this.storyFileArray.at(index).get('sfStatus')?.value === 'REJECTED';
  }


  addNewStoryFile(): FormGroup {
    const userId = localStorage.getItem('userId');
    const userName = localStorage.getItem('userName') || '';
    return this.formBuilder.group({
      sfStoryId: [this.storyId],
      sfStoryTellerId: [userId || '', Validators.required],
      sfStoryTellerName: [userName || ''],
      sfLanguageId: ['', Validators.required],
      sfFileName: [null, Validators.required],
      sfDuration: [''],
      sfTranscription: ['its a test audio'],
      encFile: [null],
      sfAuthorName: [''],
      audioSrc: [null],
      isPlaying: [false],
      isEditing: [false],
      sfStatus: [''],
    });

  }

  createStoryRow(): void {
    this.storyFileArray.push(this.addNewStoryFile());
  }

  onMetaDataSubmit() {
    this.addNewStoryForm.markAllAsTouched();

    if (this.addNewStoryForm.valid) {
      const postData = this.isEdit ? { ...this.addNewStoryForm.value, stStoryId: this.storyId,stGenreId:this.selectedGenre.genId }
        : { ...this.addNewStoryForm.value, stDp: this.selectedGenre.dp,stGenreId:this.selectedGenre.genId };
        
      this.isEdit?delete postData.stStatus:'';
      this.ngxSpinnerService.show('globalSpinner');
      const apiCall = this.isEdit
        ? this.dataTransferService.updateStoryMetaData(postData)
        : this.dataTransferService.addStoryMetaData(postData);

      apiCall.subscribe({
        next: (response: any) => {
          this.storyId = this.isEdit ? response.data.stId : response.data;
          this.toastr.success(response.message);
        },
        error: (err) => {
          this.toastr.error(err.error.message);
          console.error('Error creating or updating subscription:', err);
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    } else {
      this.toastr.error('Please fill all required fields correctly');
      return;
    }
  }

  onStoryFileSubmit(index: number) {
    this.pauseAll()
    const storyFileGroup = this.storyFileArray.at(index);
    storyFileGroup.markAllAsTouched();
    if (storyFileGroup.invalid) {
      this.toastr.error('Please fill all required fields correctly');
      return;
    }
    if (storyFileGroup.value.sfId) {
      const payload = storyFileGroup.value;
      delete payload.isPlaying;
      delete payload.audioSrc;
      delete payload.sfStoryTellerName;
      delete payload.isEditing;

      this.updateStoryFile(payload, index);
    } else {
      storyFileGroup.patchValue({
        ...storyFileGroup.value,
        sfStoryId: this.storyId,
        sfStatus: this.isAdmin ? StoryStatus.PUBLISHED : StoryStatus.DRAFT,
      });
      const payload = storyFileGroup.value;
      delete payload.isPlaying;
      delete payload.audioSrc;
      delete payload.sfStoryTellerName;
      delete payload.isEditing;

      this.addStoryFile(payload, index);
    }
  }

  async updateStoryFile(payload: any, index: number) {
    try {
      const data: any[] = [];
      data.push(payload)
      this.ngxSpinnerService.show('globalSpinner');
      await new Promise((resolve, reject) => {
        this.dataTransferService.updateStoryFile(data).subscribe({
          next: (value: any) => {
            this.toastr.success(value.message);
            const storyFileGroup = this.storyFileArray.at(index);
            storyFileGroup.get('isEditing')?.patchValue(false);
            resolve(value);
          },
          error: (err) => {
            this.toastr.error(err.error.message || 'Error occurred');
            console.error(err);
            reject(err);
          },
          complete: () => {
            this.ngxSpinnerService.hide('globalSpinner');
          },
        });
      });
    } catch (error) {
      console.error('Error updating story file:', error);
    }
  }

  async publishFile(index: number, status: string) {
    try {
      const storyFileGroup = this.storyFileArray.at(index);
      storyFileGroup.patchValue({
        sfStoryId: this.storyId,
        sfStatus: status === 'REJECTED' ? StoryStatus.REJECTED : StoryStatus.PUBLISHED,
      });
      const payload = storyFileGroup.value;
      delete payload.isPlaying;
      delete payload.audioSrc;

      await this.updateStoryFile(payload, index);
      await this.getStoryById(this.storyId);
    } catch (error) {
      console.error('Error publishing file:', error);
    }
  }


  addStoryFile(payload: any, index: number) {
    const data = [];
    data.push(payload);
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.addStoryFile(data).subscribe({
      next: (value: any) => {
        this.toastr.success(value.message);
        this.getStoryById(this.storyId);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        const storyFileGroup = this.storyFileArray.at(index);
        storyFileGroup.patchValue({
          sfStatus: '',
        });
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }



  onFileChange(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    this.pauseAllOtherAudioPlayers(index);

    if (input?.files?.length) {
      const file = input.files[0];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      if (['mp3', 'wav'].includes(fileExtension || '')) {
        const storyFileGroup = this.storyFileArray.at(index);
        const audioSrc = URL.createObjectURL(file);
        const safeAudioSrc = this.sanitizer.bypassSecurityTrustUrl(audioSrc); // Sanitize the URL

        const audio = new Audio(audioSrc);

        audio.addEventListener('loadedmetadata', () => {
          const durationInSeconds = Math.floor(audio.duration);

          const reader = new FileReader();
          reader.onload = () => {
            let base64String = reader.result as string;

            base64String = base64String.split(',')[1];

            storyFileGroup.patchValue({
              sfFileName: file.name,
              audioSrc: safeAudioSrc,
              sfDuration: durationInSeconds,
              encFile: base64String,
            });
            storyFileGroup.updateValueAndValidity();
          };

          reader.onerror = () => {
            console.error('Error reading file as Base64');
          };

          reader.readAsDataURL(file);
        });
      } else {
        this.storyFileArray.at(index).get('sfFileName')?.setErrors({ invalidFormat: true });
      }
    }
  }




  // togglePlay(index: number): void {
  //   const storyFileGroup = this.storyFileArray.at(index);
  //   const isPlaying = storyFileGroup.get('isPlaying')?.value;
  //   this.pauseAllOtherAudioPlayers(index);

  //   if (isPlaying) {
  //     this.pauseAudio(index);
  //   } else {
  //     this.playAudio(index);
  //   }
  // }

  togglePlay(index: number, audioPlayer: HTMLAudioElement) {
    const story = this.storyFileArray.at(index);
    const isPlaying = story.get('isPlaying')?.value;

    if (isPlaying) {
      audioPlayer.pause();
      story.get('isPlaying')?.setValue(false);
    } else {
      this.storyFileArray.controls.forEach((s, i) => {
        // Pause all other audio players if playing
        if (i !== index && s.get('isPlaying')?.value) {
          const otherAudio = document.querySelectorAll('audio')[i] as HTMLAudioElement;
          otherAudio.pause();
          s.get('isPlaying')?.setValue(false);
        }
      });

      audioPlayer.play();
      story.get('isPlaying')?.setValue(true);
    }
  }


  playAudio(index: number): void {
    const storyFileGroup = this.storyFileArray.at(index);
    const audioSrc = storyFileGroup.get('audioSrc')?.value;

    if (audioSrc) {
      let audioPlayer = this.audioPlayers[index];

      if (!audioPlayer) {
        audioPlayer = new Audio(audioSrc);
        this.audioPlayers[index] = audioPlayer;
      }

      audioPlayer.play();

      storyFileGroup.patchValue({ isPlaying: true });
    }
  }

  pauseAudio(index: number): void {
    const storyFileGroup = this.storyFileArray.at(index);
    const audioPlayer = this.audioPlayers[index];

    if (audioPlayer) {
      audioPlayer.pause();

      storyFileGroup.patchValue({ isPlaying: false });
    }
  }

  pauseAllOtherAudioPlayers(currentIndex: number): void {
    for (const index in this.audioPlayers) {
      if (parseInt(index) !== currentIndex) {
        const otherAudioPlayer = this.audioPlayers[parseInt(index)];
        if (otherAudioPlayer) {
          otherAudioPlayer.pause();
          const storyFileGroup = this.storyFileArray.at(parseInt(index));
          storyFileGroup.patchValue({ isPlaying: false });
        }
      }
    }
  }

  pauseAll() {
    for (const index in this.audioPlayers) {
      const AudioPlayer = this.audioPlayers[parseInt(index)];
      if (AudioPlayer) {
        AudioPlayer.pause();
        const storyFileGroup = this.storyFileArray.at(parseInt(index));
        storyFileGroup.patchValue({ isPlaying: false });
      }
    }
  }

  getStoryById(storyId: any): void {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getStoryById(storyId).subscribe({
      next: (value: any) => {
        if (value.data) {
          const data = value?.data;
          this.isStoryCreatedByLoggedInUser = (this.loggedInUserId == parseInt(data?.stCreatedBy));
          this.metaData = {
            stTitle: data?.stTitle,
            stDescription: data?.stDescription,
            stGenreId: data?.stGenreId,
            ageGroupId: data?.stAgeGroupName?.map((group: any) => group.sm_entityid),
            stTags: data?.stTags,
            stStatus: data?.stStatus,
          };

          this.addNewStoryForm.patchValue(this.metaData);

          const storyFilesFormArray = this.storyFileForm.get('storyFiles') as FormArray;
          if (data.storyFiles && data?.storyFiles?.length > 0) storyFilesFormArray.clear();

          data.storyFiles.forEach((file: any) => {
            const storyFileGroup = this.formBuilder.group({
              sfStoryTellerId: [file?.storyTellerDetail?.userId || null, Validators.required],
              sfStoryTellerName: [file?.storyTellerDetail?.name || null],
              sfLanguageId: [file?.sfLanguageId, Validators.required],
              sfFileName: [file?.sfFileName, Validators.required],
              sfAuthorName: [file?.sfAuthorName || ''],
              audioSrc: [this.s3BucketUrl + file?.sfFileName],
              sfStatus: [file?.sfStatus],
              sfDuration: [file?.sfDuration],
              sfTranscription: [file?.sfTranscription],
              isPlaying: [false],
              isEditing: [false],
              sfStoryId: [file?.sfStoryId],
              sfId: [file?.sfId || ''],
              encFile: [file?.encFile]
            });
            storyFilesFormArray.push(storyFileGroup);
          });
        }
        this.cdr.detectChanges();
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }


  getAllLanguages() {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllLanguages(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.languageData = value.data;
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getAllGenres() {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllGenres(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.genresData = value?.data;
          this.cdr.detectChanges();
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getAllAgeGroup() {
    const payload = {
      limit: this.maxNumber,
      offset: '',
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllAgeGroup(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.ageGroupData = value?.data;
          this.cdr.detectChanges();
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  toggleEditMode(index: number): void {
    const story = this.storyFileArray.at(index);
    const isEditing = story.get('isEditing')?.value;
    story.patchValue({ isEditing: !isEditing });
  }

  getAllAuthors() {
    const payload = {
      search: '',
      limit: this.maxNumber,
      offset: '',
      userRole: UserTypes.AUTHOR,
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllUsers(payload).subscribe({
      next: (value: any) => {
        this.totalNumberOfRecords = value?.count;
        this.authorData = value?.Data
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getAllStoryTellers() {
    const payload = {
      search: '',
      limit: this.maxNumber,
      offset: '',
      userRole: UserTypes.STORYTELLER,
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllUsers(payload).subscribe({
      next: (value: any) => {
        this.storytellerData = value?.Data
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onBackClick() {
    if (this.isFromRequestComp) {
      this.router.navigate(['/stories/requests'], { state: { isEdit: this.isEdit, searchTerm: this.searchTerm, isFromRequestComp: this.isFromRequestComp } });
    } else {
      this.router.navigate(['/stories'], { state: { isEdit: this.isEdit, searchTerm: this.searchTerm, isFromRequestComp: this.isFromRequestComp } });
    }
  }


  onGenreSelected(genre: any) {
    this.selectedGenre=genre;
  }

}
