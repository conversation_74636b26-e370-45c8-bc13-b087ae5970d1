<!-- <div class="container-scroller"> -->
<div class="container-fluid page-body-wrapper">

  <nav class="sidebar sidebar-offcanvas py-3" id="sidebar">
    <div class="d-flex justify-content-center align-items-center">
      <img height="55px" src="./assets/images/katha-mehfil-main-logo.svg" alt="Logo" />
    </div>
    <div class="m-0 p-0 sidebar-btn-container">
    <ul class="nav py-2">
      <li
        class="nav-item mb-3"
        *ngFor="let menu of accessMenuList"
        [class.active-menu]="isParentMenuActive(menu) || menu.isOpen"
        (mouseover)="onHover(menu)"
        (mouseout)="onMouseOut(menu)">
        <a *ngIf="!menu.subMenu"
          class="nav-link"
          [routerLink]="menu.route"
          routerLinkActive="active-menu">
          <img
            class="sidebar-icon"
            [src]="getIcon(menu)"
            alt="{{ menu.name }}"/>
          <span class="menu-title">{{ menu.name }}</span>
        </a>
  
        <a
          *ngIf="menu.subMenu"
          class="nav-link"
          (click)="toggleSubMenu(menu); $event.preventDefault()"
          [class.active-menu]="isParentMenuActive(menu) || menu.isOpen">
          <img
            class="sidebar-icon"
            [src]="getIcon(menu)"
            alt="{{ menu.name }}"/>
          <span class="menu-title">{{ menu.name }}</span>
          <i
            class="fa-solid"
            [class.fa-angle-down]="menu.isOpen"
            [class.fa-angle-right]="!menu.isOpen"
          ></i>
        </a>
  
        <ul *ngIf="menu.subMenu && menu.isOpen" class="submenu">
          <li
            class="nav-item"
            *ngFor="let subMenu of menu.subMenu"
            [class.active-menu]="isSubmenuActive(subMenu.route)"
            (mouseover)="onHover(subMenu)"
            (mouseout)="onMouseOut(subMenu)"
          >
            <a
              class="nav-link"
              [routerLink]="subMenu.route"
              routerLinkActive="active-menu"
            >
              <img
                class="sidebar-icon"
                [src]="getIcon(subMenu)"
                alt="{{ subMenu.name }}"
              />
              <span class="menu-title">{{ subMenu.name }}</span>
            </a>
          </li>
        </ul>
      </li>
  
      <!-- Logout Button -->
    </ul>

    <ul class="nav py-2">
      <li class="nav-item logout-btn">
        <a
          class="nav-link btn btn-outline-primary"
          (click)="showModal()"
          style="cursor: pointer;"
        >
          <img
            class="sidebar-icon"
            [src]="'./assets/icons/Logout.svg'"
            alt="Logout"
          />
          <span class="menu-title">Logout</span>
        </a>
      </li>
    </ul>
  </div>
  </nav>
  
  


  <div style="overflow-y: auto;" class="main-panel">
    <nav class="navbar mb-5 px-3">
      <!-- <div class="navbar-menu-wrapper d-flex align-items-center"> -->
      <span class="menuTitle ">{{menuTitle}}</span>
      <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button"
        (click)="toggleSidebar()">
        <span class="icon-menu"></span>
      </button>
      <!-- </div> -->
    </nav>
    <ng-content></ng-content>
    <footer class="footer mt-5">
      <span class="footer-text">© {{currentYear | date: 'yyyy'}} by Katha Mehfil, Inc</span>
    </footer>
  </div>

  <!-- </div> -->
  <!-- </div> -->


  <div class="modal" id="logOutModal">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content katha-modal">
        <div class="modal-header" style="border-bottom: none;">
          <h5 class="modal-title w-100 text-center">
            Are you sure you want to logout?
          </h5>
          <button type="button" (click)="hideModal()" class="close" data-bs-dismiss="modal">&times;</button>
        </div>
        <div class="modal-footer justify-content-center" style="border-top: none;">
          <button type="button" class="btn btn-outline-secondary" (click)="hideModal()">Cancel</button>
          <button (click)="logout()" type="submit" class="btn btn-outline-primary">Logout</button>
        </div>
      </div>
    </div>
  </div>