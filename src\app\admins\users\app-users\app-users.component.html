<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">

        <!-- <div>
            <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
            </app-breadcrumb>
        </div> -->
        
        <div class="row mb-4 head-Home">
            <div class="col-lg-3 position-relative">
                <app-search-box (searchValue)="onSearch($event)"></app-search-box>
            </div>
            <div class="col-lg-6 position-relative">
            </div>
            
        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookClubId" [columns]="columns"
            [actionPermissions]="false" [data]="appUsers" [onSwitchUpdate]="lockUnlockUser"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord" [activeLabel]="activeLabel" [inactiveLabel]="inactiveLabel"
            [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged"
            [module]="_tomodule" modalId="addSubscriptionPlanFormModal" (showOtherModal)="handleOpenModal($event)"></app-list-with-pagination>


      
           
       </div>
</app-sidebar>