.fileUploader {
    position: relative;
}

.fileUploadIcon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    width: 24px;
    height: 20px;
    pointer-events: none;
}



.fileName {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    font-size: small;
}


audio {
    width: 250px;
    height: 40px;
}

.audioGroup{
    display: flex;
    flex-direction: row;
}

@media (max-width: 768px) {
    .audioGroup{
    flex-direction: column;
        }
}